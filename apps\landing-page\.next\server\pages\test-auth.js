"use strict";(()=>{var e={};e.id=664,e.ids=[664,888],e.modules={7711:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>x,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>v,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>S});var a=r(8877),o=r(4591),i=r(6021),n=r(479),l=r(4801),c=r(1714),u=e([l,c]);[l,c]=u.then?(await u)():u;let d=(0,i.l)(c,"default"),p=(0,i.l)(c,"getStaticProps"),x=(0,i.l)(c,"getStaticPaths"),h=(0,i.l)(c,"getServerSideProps"),m=(0,i.l)(c,"config"),g=(0,i.l)(c,"reportWebVitals"),S=(0,i.l)(c,"unstable_getStaticProps"),b=(0,i.l)(c,"unstable_getStaticPaths"),f=(0,i.l)(c,"unstable_getStaticParams"),j=(0,i.l)(c,"unstable_getServerProps"),P=(0,i.l)(c,"unstable_getServerSideProps"),v=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/test-auth",pathname:"/test-auth",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:c});s()}catch(e){s(e)}})},1714:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>x,getStaticProps:()=>p});var a=r(997),o=r(6689),i=r(5460),n=r(1377),l=r(1675),c=r(5392),u=r(785),d=e([c,u]);[c,u]=d.then?(await d)():d;let p=async({locale:e})=>({props:{...await (0,i.serverSideTranslations)(e??"ar",["auth","common"])}}),x=()=>{let[e,t]=(0,o.useState)(!1),[r,s]=(0,o.useState)(!1),{t:i}=(0,n.useTranslation)(["auth","common"]),d=e=>{console.log("Auth success:",e),alert(`Authentication successful! Welcome ${e.user.email}`)},p=e=>{console.error("Auth error:",e),alert(`Authentication error: ${e}`)};return a.jsx(l.f6,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center p-4",children:[(0,a.jsxs)("div",{className:"max-w-md w-full space-y-4",children:[a.jsx("h1",{className:"text-3xl font-bold text-white text-center mb-8",children:i("common:testAuthentication")}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("button",{onClick:()=>t(!0),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:"Test Sign In Modal"}),a.jsx("button",{onClick:()=>s(!0),className:"w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:"Test Sign Up Modal"})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-gray-800 rounded-lg",children:[a.jsx("h2",{className:"text-lg font-semibold text-white mb-2",children:"Test Instructions:"}),(0,a.jsxs)("ul",{className:"text-gray-300 text-sm space-y-1",children:[a.jsx("li",{children:"• Click buttons to open authentication modals"}),a.jsx("li",{children:"• Test Google OAuth integration"}),a.jsx("li",{children:"• Verify glass morphism effects"}),a.jsx("li",{children:"• Check Arabic RTL layout"}),a.jsx("li",{children:"• Test dual-theme support (Gold/Purple)"})]})]})]}),a.jsx(c.Z,{isOpen:e,onClose:()=>t(!1),onSuccess:d,onError:p,onSwitchToSignUp:()=>{t(!1),s(!0)}}),a.jsx(u.Z,{isOpen:r,onClose:()=>s(!1),onSuccess:d,onError:p,onSwitchToSignIn:()=>{s(!1),t(!0)}})]})})};s()}catch(e){s(e)}})},1649:e=>{e.exports=require("next-auth/react")},1377:e=>{e.exports=require("next-i18next")},5460:e=>{e.exports=require("next-i18next/serverSideTranslations")},1162:e=>{e.exports=require("next-themes")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},8316:e=>{e.exports=require("zod")},6197:e=>{e.exports=import("framer-motion")},6201:e=>{e.exports=import("react-hot-toast")},9926:e=>{e.exports=import("zod")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[217,464,331,952,801,931],()=>r(7711));module.exports=s})();