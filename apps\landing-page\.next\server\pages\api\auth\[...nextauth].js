"use strict";(()=>{var e={};e.id=748,e.ids=[748],e.modules={3524:e=>{e.exports=require("@prisma/client")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6835:(e,a)=>{Object.defineProperty(a,"l",{enumerable:!0,get:function(){return function e(a,t){return t in a?a[t]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,t)):"function"==typeof a&&"default"===t?a:void 0}}})},1780:(e,a,t)=>{t.r(a),t.d(a,{config:()=>b,default:()=>P,routeModule:()=>h});var r={};t.r(r),t.d(r,{authOptions:()=>g,default:()=>f});var n=t(9150),i=t(1631),s=t(6835);let o=require("next-auth");var c=t.n(o);let l=require("next-auth/providers/google");var u=t.n(l);let d=require("@next-auth/prisma-adapter");var m=t(8076),p=t(3524);let g={adapter:(0,d.PrismaAdapter)(m.prisma),providers:[u()({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||"",authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],pages:{signIn:"/",error:"/auth/error"},callbacks:{async signIn({user:e,account:a,profile:t}){try{if(a?.provider==="google"){let a=await m.prisma.user.findUnique({where:{email:e.email||""}});a?await m.prisma.user.update({where:{email:e.email||""},data:{avatar:e.image?{url:e.image}:a.avatar||p.Prisma.JsonNull,emailVerified:a.emailVerified||!0,lastLoginAt:new Date}}):await m.prisma.user.create({data:{email:e.email||"",firstName:t?.given_name||e.name?.split(" ")[0]||"",lastName:t?.family_name||e.name?.split(" ").slice(1).join(" ")||"",avatar:e.image?{url:e.image}:p.Prisma.JsonNull,emailVerified:!0,status:"ACTIVE",role:"CLIENT",language:"ar",passwordHash:""}})}return!0}catch(e){return!1}},async jwt({token:e,user:a}){if(a){let t=await m.prisma.user.findUnique({where:{email:a.email||""},select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0,status:!0,avatar:!0,language:!0}});t&&(e.id=t.id,e.role=t.role,e.status=t.status,e.language=t.language,e.firstName=t.firstName,e.lastName=t.lastName,e.avatar=t.avatar?JSON.stringify(t.avatar):void 0)}return e},session:async({session:e,token:a})=>(a&&(e.user.id=a.id,e.user.role=a.role,e.user.status=a.status,e.user.language=a.language,e.user.firstName=a.firstName,e.user.lastName=a.lastName,e.user.avatar=a.avatar),e),redirect:async({url:e,baseUrl:a})=>e.startsWith("/")?`${a}${e}`:new URL(e).origin===a?e:`${a}/dashboard`},session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},events:{async signIn({user:e,account:a}){e.email&&await m.prisma.user.update({where:{email:e.email},data:{lastLoginAt:new Date}})},async signOut({token:e}){}},debug:!1},f=c()(g),P=(0,s.l)(r,"default"),b=(0,s.l)(r,"config"),h=new n.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/auth/[...nextauth]",pathname:"/api/auth/[...nextauth]",bundlePath:"",filename:""},userland:r})},1631:(e,a)=>{var t;Object.defineProperty(a,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},9150:(e,a,t)=>{e.exports=t(145)},4301:(e,a,t)=>{let r;Object.defineProperty(a,"__esModule",{value:!0}),a.PrismaClient=a.Prisma=a.getPrismaInstance=a.withTransaction=a.checkDatabaseHealth=a.disconnectDatabase=a.connectDatabase=a.prisma=void 0;let n=t(3524);Object.defineProperty(a,"PrismaClient",{enumerable:!0,get:function(){return n.PrismaClient}}),Object.defineProperty(a,"Prisma",{enumerable:!0,get:function(){return n.Prisma}});let i=()=>{if(r)return r;let e=new n.PrismaClient({log:["error"],errorFormat:"pretty"});return r=e};a.getPrismaInstance=i;let s=i();a.prisma=s,(e=>{let a=!1,t=async t=>{a||(a=!0,console.log(`Received ${t}. Disconnecting database...`),await e.$disconnect(),console.log("Database disconnected."),process.exit(0))};process.on("SIGINT",()=>t("SIGINT")),process.on("SIGTERM",()=>t("SIGTERM"))})(s);let o=async()=>{let e=i();try{await Promise.race([e.$connect(),new Promise((e,a)=>setTimeout(()=>a(Error("Database connection timed out after 10 seconds")),1e4))]),console.log("✅ Database connected successfully")}catch(e){e instanceof Error?console.error("❌ Database connection failed:",e.message):console.error("❌ An unexpected error occurred during database connection:",e),console.warn("⚠️ Server is starting without a database connection. Some features will be unavailable.");return}};a.connectDatabase=o;let c=async()=>{let e=i();try{await e.$disconnect(),console.log("✅ Database disconnected successfully")}catch(e){throw e instanceof Error?console.error("❌ Database disconnection failed:",e.message):console.error("❌ An unexpected error occurred during database disconnection:",e),e}};a.disconnectDatabase=c;let l=async()=>{let e=i();try{return await e.$queryRaw`SELECT 1`,!0}catch(e){return!1}};a.checkDatabaseHealth=l;let u=async e=>{let a=i();return await a.$transaction(e)};a.withTransaction=u,a.default=s},8076:function(e,a,t){var r=this&&this.__createBinding||(Object.create?function(e,a,t,r){void 0===r&&(r=t);var n=Object.getOwnPropertyDescriptor(a,t);(!n||("get"in n?!a.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return a[t]}}),Object.defineProperty(e,r,n)}:function(e,a,t,r){void 0===r&&(r=t),e[r]=a[t]}),n=this&&this.__exportStar||function(e,a){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(a,t)||r(a,e,t)};Object.defineProperty(a,"__esModule",{value:!0}),n(t(4301),a)}};var a=require("../../../webpack-api-runtime.js");a.C(e);var t=a(a.s=1780);module.exports=t})();