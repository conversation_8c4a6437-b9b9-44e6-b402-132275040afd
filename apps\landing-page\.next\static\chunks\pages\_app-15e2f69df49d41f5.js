(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{3463:function(e,t,r){"use strict";var n=r(8570),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function l(e){return n.isMemo(e)?s:a[e.$$typeof]||i}a[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[n.Memo]=s;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(f){var i=p(r);i&&i!==f&&e(t,i,n)}var s=c(r);d&&(s=s.concat(d(r)));for(var a=l(t),m=l(r),g=0;g<s.length;++g){var y=s[g];if(!o[y]&&!(n&&n[y])&&!(m&&m[y])&&!(a&&a[y])){var v=h(r,y);try{u(t,y,v)}catch(e){}}}}return t}},6483:function(e,t,r){"use strict";var n=r(1600);Object.defineProperty(t,"__esModule",{value:!0}),t.BroadcastChannel=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"nextauth.message";return{receive:function(t){var r=function(r){if(r.key===e){var n,i=JSON.parse(null!==(n=r.newValue)&&void 0!==n?n:"{}");(null==i?void 0:i.event)==="session"&&null!=i&&i.data&&t(i)}};return window.addEventListener("storage",r),function(){return window.removeEventListener("storage",r)}},post:function(t){if("undefined"!=typeof window)try{localStorage.setItem(e,JSON.stringify(l(l({},t),{},{timestamp:d()})))}catch(e){}}}},t.apiBaseUrl=c,t.fetchData=function(e,t,r){return u.apply(this,arguments)},t.now=d;var i=n(r(2841)),o=n(r(6290)),s=n(r(1461));function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(){return(u=(0,s.default)(i.default.mark(function e(t,r,n){var o,s,a,u,d,h,p,f,m,g=arguments;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=(o=g.length>3&&void 0!==g[3]?g[3]:{}).ctx,u=void 0===(a=o.req)?null==s?void 0:s.req:a,d="".concat(c(r),"/").concat(t),e.prev=2,p={headers:l({"Content-Type":"application/json"},null!=u&&null!==(h=u.headers)&&void 0!==h&&h.cookie?{cookie:u.headers.cookie}:{})},null!=u&&u.body&&(p.body=JSON.stringify(u.body),p.method="POST"),e.next=7,fetch(d,p);case 7:return f=e.sent,e.next=10,f.json();case 10:if(m=e.sent,f.ok){e.next=13;break}throw m;case 13:return e.abrupt("return",Object.keys(m).length>0?m:null);case 16:return e.prev=16,e.t0=e.catch(2),n.error("CLIENT_FETCH_ERROR",{error:e.t0,url:d}),e.abrupt("return",null);case 20:case"end":return e.stop()}},e,null,[[2,16]])}))).apply(this,arguments)}function c(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function d(){return Math.floor(Date.now()/1e3)}},7693:function(e,t,r){"use strict";var n=r(1600);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,n){return r[n]=(0,o.default)(i.default.mark(function r(){var o,s,a,l,u,c=arguments;return i.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,s=Array(o=c.length),a=0;a<o;a++)s[a]=c[a];return t.debug("adapter_".concat(n),{args:s}),l=e[n],r.next=6,l.apply(void 0,s);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(n),r.t0),(u=new m(r.t0)).name="".concat(y(n),"Error"),u;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=y,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,n){return r[n]=(0,o.default)(i.default.mark(function r(){var o,s=arguments;return i.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,o=e[n],r.next=4,o.apply(void 0,s);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(g(n),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=g;var i=n(r(2841)),o=n(r(1461)),s=n(r(6290)),a=n(r(3100)),l=n(r(8870)),u=n(r(421)),c=n(r(1147)),d=n(r(8230)),h=n(r(8365));function p(e,t,r){return t=(0,c.default)(t),(0,u.default)(e,f()?Reflect.construct(t,r||[],(0,c.default)(e).constructor):t.apply(e,r))}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(f=function(){return!!e})()}var m=t.UnknownError=function(e){function t(e){var r,n;return(0,a.default)(this,t),(n=p(this,t,[null!==(r=null==e?void 0:e.message)&&void 0!==r?r:e])).name="UnknownError",n.code=e.code,e instanceof Error&&(n.stack=e.stack),n}return(0,d.default)(t,e),(0,l.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,h.default)(Error));function g(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function y(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","OAuthCallbackError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.AccountNotLinkedError=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","AccountNotLinkedError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAPIRoute=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAPIRouteError"),(0,s.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingSecret=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","MissingSecretError"),(0,s.default)(e,"code","NO_SECRET"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAuthorize=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAuthorizeError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAdapter=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAdapterError"),(0,s.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAdapterMethods=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAdapterMethodsError"),(0,s.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.UnsupportedStrategy=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","UnsupportedStrategyError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.InvalidCallbackUrl=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=p(this,t,[].concat(n)),(0,s.default)(e,"name","InvalidCallbackUrl"),(0,s.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m)},3848:function(e,t,r){"use strict";var n,i,o,s,a,l=r(3542),u=r(1600),c=r(7425);Object.defineProperty(t,"__esModule",{value:!0});var d={SessionContext:!0,useSession:!0,getSession:!0,getCsrfToken:!0,getProviders:!0,signIn:!0,signOut:!0,SessionProvider:!0};t.SessionContext=void 0,t.SessionProvider=function(e){if(!T)throw Error("React Context is unavailable in Server Components");var t,r,n,i,o,s,a=e.children,l=e.basePath,u=e.refetchInterval,c=e.refetchWhenOffline;l&&(O.basePath=l);var d=void 0!==e.session;O._lastSync=d?(0,b.now)():0;var p=g.useState(function(){return d&&(O._session=e.session),e.session}),y=(0,m.default)(p,2),v=y[0],w=y[1],S=g.useState(!d),k=(0,m.default)(S,2),P=k[0],E=k[1];g.useEffect(function(){return O._getSession=(0,f.default)(h.default.mark(function e(){var t,r,n=arguments;return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(n.length>0&&void 0!==n[0]?n[0]:{}).event,e.prev=1,!((r="storage"===t)||void 0===O._session)){e.next=10;break}return O._lastSync=(0,b.now)(),e.next=7,L({broadcast:!r});case 7:return O._session=e.sent,w(O._session),e.abrupt("return");case 10:if(!(!t||null===O._session||(0,b.now)()<O._lastSync)){e.next=12;break}return e.abrupt("return");case 12:return O._lastSync=(0,b.now)(),e.next=15,L();case 15:O._session=e.sent,w(O._session),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(1),C.error("CLIENT_SESSION_ERROR",e.t0);case 22:return e.prev=22,E(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[1,19,22,25]])})),O._getSession(),function(){O._lastSync=0,O._session=void 0,O._getSession=function(){}}},[]),g.useEffect(function(){var e=j.receive(function(){return O._getSession({event:"storage"})});return function(){return e()}},[]),g.useEffect(function(){var t=e.refetchOnWindowFocus,r=void 0===t||t,n=function(){r&&"visible"===document.visibilityState&&O._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",n,!1),function(){return document.removeEventListener("visibilitychange",n,!1)}},[e.refetchOnWindowFocus]);var A=(t=g.useState("undefined"!=typeof navigator&&navigator.onLine),n=(r=(0,m.default)(t,2))[0],i=r[1],o=function(){return i(!0)},s=function(){return i(!1)},g.useEffect(function(){return window.addEventListener("online",o),window.addEventListener("offline",s),function(){window.removeEventListener("online",o),window.removeEventListener("offline",s)}},[]),n),M=!1!==c||A;g.useEffect(function(){if(u&&M){var e=setInterval(function(){O._session&&O._getSession({event:"poll"})},1e3*u);return function(){return clearInterval(e)}}},[u,M]);var N=g.useMemo(function(){return{data:v,status:P?"loading":v?"authenticated":"unauthenticated",update:function(e){return(0,f.default)(h.default.mark(function t(){var r;return h.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(P||!v)){t.next=2;break}return t.abrupt("return");case 2:return E(!0),t.t0=b.fetchData,t.t1=O,t.t2=C,t.next=8,R();case 8:return t.t3=t.sent,t.t4=e,t.t5={csrfToken:t.t3,data:t.t4},t.t6={body:t.t5},t.t7={req:t.t6},t.next=15,(0,t.t0)("session",t.t1,t.t2,t.t7);case 15:return r=t.sent,E(!1),r&&(w(r),j.post({event:"session",data:{trigger:"getSession"}})),t.abrupt("return",r);case 19:case"end":return t.stop()}},t)}))()}}},[v,P]);return(0,x.jsx)(T.Provider,{value:N,children:a})},t.getCsrfToken=R,t.getProviders=N,t.getSession=L,t.signIn=function(e,t,r){return D.apply(this,arguments)},t.signOut=function(e){return V.apply(this,arguments)},t.useSession=function(e){if(!T)throw Error("React Context is unavailable in Server Components");var t=g.useContext(T),r=null!=e?e:{},n=r.required,i=r.onUnauthenticated,o=n&&"unauthenticated"===t.status;return(g.useEffect(function(){if(o){var e="/api/auth/signin?".concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));i?i():window.location.href=e}},[o,i]),o)?{data:t.data,update:t.update,status:"loading"}:t};var h=u(r(2841)),p=u(r(6290)),f=u(r(1461)),m=u(r(3681)),g=k(r(2784)),y=k(r(4339)),v=u(r(8203)),b=r(6483),x=r(2322),w=r(2878);function S(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(S=function(e){return e?r:t})(e)}function k(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=c(e)&&"function"!=typeof e)return{default:e};var r=S(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach(function(t){(0,p.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.keys(w).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(d,e))&&(e in t&&t[e]===w[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return w[e]}}))});var O={baseUrl:(0,v.default)(null!==(n=l.env.NEXTAUTH_URL)&&void 0!==n?n:l.env.VERCEL_URL).origin,basePath:(0,v.default)(l.env.NEXTAUTH_URL).path,baseUrlServer:(0,v.default)(null!==(i=null!==(o=l.env.NEXTAUTH_URL_INTERNAL)&&void 0!==o?o:l.env.NEXTAUTH_URL)&&void 0!==i?i:l.env.VERCEL_URL).origin,basePathServer:(0,v.default)(null!==(s=l.env.NEXTAUTH_URL_INTERNAL)&&void 0!==s?s:l.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:function(){}},j=(0,b.BroadcastChannel)(),C=(0,y.proxyLogger)(y.default,O.basePath),T=t.SessionContext=null===(a=g.createContext)||void 0===a?void 0:a.call(g,void 0);function L(e){return A.apply(this,arguments)}function A(){return(A=(0,f.default)(h.default.mark(function e(t){var r,n;return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,b.fetchData)("session",O,C,t);case 2:return n=e.sent,(null===(r=null==t?void 0:t.broadcast)||void 0===r||r)&&j.post({event:"session",data:{trigger:"getSession"}}),e.abrupt("return",n);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function R(e){return M.apply(this,arguments)}function M(){return(M=(0,f.default)(h.default.mark(function e(t){var r;return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,b.fetchData)("csrf",O,C,t);case 2:return r=e.sent,e.abrupt("return",null==r?void 0:r.csrfToken);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function N(){return _.apply(this,arguments)}function _(){return(_=(0,f.default)(h.default.mark(function e(){return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,b.fetchData)("providers",O,C);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function D(){return(D=(0,f.default)(h.default.mark(function e(t,r,n){var i,o,s,a,l,u,c,d,p,f,m,g,y,v,x,w,S;return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=void 0===(o=(i=null!=r?r:{}).callbackUrl)?window.location.href:o,l=void 0===(a=i.redirect)||a,u=(0,b.apiBaseUrl)(O),e.next=4,N();case 4:if(c=e.sent){e.next=8;break}return window.location.href="".concat(u,"/error"),e.abrupt("return");case 8:if(!(!t||!(t in c))){e.next=11;break}return window.location.href="".concat(u,"/signin?").concat(new URLSearchParams({callbackUrl:s})),e.abrupt("return");case 11:return d="credentials"===c[t].type,p="email"===c[t].type,f=d||p,m="".concat(u,"/").concat(d?"callback":"signin","/").concat(t),g="".concat(m).concat(n?"?".concat(new URLSearchParams(n)):""),e.t0=fetch,e.t1=g,e.t2={"Content-Type":"application/x-www-form-urlencoded"},e.t3=URLSearchParams,e.t4=E,e.t5=E({},r),e.t6={},e.next=25,R();case 25:return e.t7=e.sent,e.t8=s,e.t9={csrfToken:e.t7,callbackUrl:e.t8,json:!0},e.t10=(0,e.t4)(e.t5,e.t6,e.t9),e.t11=new e.t3(e.t10),e.t12={method:"post",headers:e.t2,body:e.t11},e.next=33,(0,e.t0)(e.t1,e.t12);case 33:return y=e.sent,e.next=36,y.json();case 36:if(v=e.sent,!(l||!f)){e.next=42;break}return w=null!==(x=v.url)&&void 0!==x?x:s,window.location.href=w,w.includes("#")&&window.location.reload(),e.abrupt("return");case 42:if(S=new URL(v.url).searchParams.get("error"),!y.ok){e.next=46;break}return e.next=46,O._getSession({event:"storage"});case 46:return e.abrupt("return",{error:S,status:y.status,ok:y.ok,url:S?null:v.url});case 47:case"end":return e.stop()}},e)}))).apply(this,arguments)}function V(){return(V=(0,f.default)(h.default.mark(function e(t){var r,n,i,o,s,a,l,u,c;return h.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=void 0===(n=(null!=t?t:{}).callbackUrl)?window.location.href:n,o=(0,b.apiBaseUrl)(O),e.t0={"Content-Type":"application/x-www-form-urlencoded"},e.t1=URLSearchParams,e.next=6,R();case 6:return e.t2=e.sent,e.t3=i,e.t4={csrfToken:e.t2,callbackUrl:e.t3,json:!0},e.t5=new e.t1(e.t4),s={method:"post",headers:e.t0,body:e.t5},e.next=13,fetch("".concat(o,"/signout"),s);case 13:return a=e.sent,e.next=16,a.json();case 16:if(l=e.sent,j.post({event:"session",data:{trigger:"signout"}}),!(null===(r=null==t?void 0:t.redirect)||void 0===r||r)){e.next=23;break}return c=null!==(u=l.url)&&void 0!==u?u:i,window.location.href=c,c.includes("#")&&window.location.reload(),e.abrupt("return");case 23:return e.next=25,O._getSession({event:"storage"});case 25:return e.abrupt("return",l);case 26:case"end":return e.stop()}},e)}))).apply(this,arguments)}},2878:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},4339:function(e,t,r){"use strict";var n=r(1600);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},n=function(e){var n;r[e]=(n=(0,s.default)(i.default.mark(function r(n,s){var a,d;return i.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(c[e](n,s),"error"===e&&(s=u(s)),s.client=!0,a="".concat(t,"/_log"),d=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:n},s)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(a,d));case 8:return r.next=10,fetch(a,{method:"POST",body:d,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return n.apply(this,arguments)})};for(var a in e)n(a);return r}catch(e){return c}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(c.debug=function(){}),e.error&&(c.error=e.error),e.warn&&(c.warn=e.warn),e.debug&&(c.debug=e.debug)};var i=n(r(2841)),o=n(r(6290)),s=n(r(1461)),a=r(7693);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){var t;return e instanceof Error&&!(e instanceof a.UnknownError)?{message:e.message,stack:e.stack,name:e.name}:(null!=e&&e.error&&(e.error=u(e.error),e.message=null!==(t=e.message)&&void 0!==t?t:e.error.message),e)}var c={error:function(e,t){t=u(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=c},8203:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!==(t=e)&&void 0!==t?t:r),i=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),o=`${n.origin}${i}`;return{origin:n.origin,host:n.host,path:i,base:o,toString:()=>o}}},34:function(e,t,r){"use strict";let n;r.d(t,{Jc:function(){return eI},$G:function(){return y}});var i=r(2784);r(4896),Object.create(null);let o={};function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];"string"==typeof t[0]&&o[t[0]]||("string"==typeof t[0]&&(o[t[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];"string"==typeof t[0]&&(t[0]=`react-i18next:: ${t[0]}`),console.warn(...t)}}(...t))}let a=(e,t)=>()=>{if(e.isInitialized)t();else{let r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}};function l(e,t,r){e.loadNamespaces(t,a(e,r))}function u(e,t,r,n){"string"==typeof r&&(r=[r]),r.forEach(t=>{0>e.options.ns.indexOf(t)&&e.options.ns.push(t)}),e.loadLanguages(t,a(e,n))}let c=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,d={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},h=e=>d[e],p={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(c,h)},f=(0,i.createContext)();class m{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let g=(e,t)=>{let r=(0,i.useRef)();return(0,i.useEffect)(()=>{r.current=t?r.current:e},[e,t]),r.current};function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{i18n:r}=t,{i18n:o,defaultNS:a}=(0,i.useContext)(f)||{},c=r||o||n;if(c&&!c.reportNamespaces&&(c.reportNamespaces=new m),!c){s("You will need to pass in an i18next instance by using initReactI18next");let e=(e,t)=>"string"==typeof t?t:t&&"object"==typeof t&&"string"==typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}c.options.react&&void 0!==c.options.react.wait&&s("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let d={...p,...c.options.react,...t},{useSuspense:h,keyPrefix:y}=d,v=e||a||c.options&&c.options.defaultNS;v="string"==typeof v?[v]:v||["translation"],c.reportNamespaces.addUsedNamespaces&&c.reportNamespaces.addUsedNamespaces(v);let b=(c.isInitialized||c.initializedStoreOnce)&&v.every(e=>(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t.languages||!t.languages.length)return s("i18n.languages were undefined or empty",t.languages),!0;let n=void 0!==t.options.ignoreJSONStructure;return n?t.hasLoadedNamespace(e,{lng:r.lng,precheck:(t,n)=>{if(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!n(t.isLanguageChangingTo,e))return!1}}):function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t.languages[0],i=!!t.options&&t.options.fallbackLng,o=t.languages[t.languages.length-1];if("cimode"===n.toLowerCase())return!0;let s=(e,r)=>{let n=t.services.backendConnector.state[`${e}|${r}`];return -1===n||2===n};return(!(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1)||!t.services.backendConnector.backend||!t.isLanguageChangingTo||!!s(t.isLanguageChangingTo,e))&&!!(t.hasResourceBundle(n,e)||!t.services.backendConnector.backend||t.options.resources&&!t.options.partialBundledLanguages||s(n,e)&&(!i||s(o,e)))}(e,t,r)})(e,c,d));function x(){return c.getFixedT(t.lng||null,"fallback"===d.nsMode?v:v[0],y)}let[w,S]=(0,i.useState)(x),k=v.join();t.lng&&(k=`${t.lng}${k}`);let P=g(k),E=(0,i.useRef)(!0);(0,i.useEffect)(()=>{let{bindI18n:e,bindI18nStore:r}=d;function n(){E.current&&S(x)}return E.current=!0,b||h||(t.lng?u(c,t.lng,v,()=>{E.current&&S(x)}):l(c,v,()=>{E.current&&S(x)})),b&&P&&P!==k&&E.current&&S(x),e&&c&&c.on(e,n),r&&c&&c.store.on(r,n),()=>{E.current=!1,e&&c&&e.split(" ").forEach(e=>c.off(e,n)),r&&c&&r.split(" ").forEach(e=>c.store.off(e,n))}},[c,k]);let O=(0,i.useRef)(!0);(0,i.useEffect)(()=>{E.current&&!O.current&&S(x),O.current=!1},[c,y]);let j=[w,c,b];if(j.t=w,j.i18n=c,j.ready=b,b||!b&&!h)return j;throw new Promise(e=>{t.lng?u(c,t.lng,v,()=>e()):l(c,v,()=>e())})}function v(e){let{i18n:t,defaultNS:r,children:n}=e,o=(0,i.useMemo)(()=>({i18n:t,defaultNS:r}),[t,r]);return(0,i.createElement)(f.Provider,{value:o},n)}function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function w(e,t,r){var n;return(n=function(e,t){if("object"!=x(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=x(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==x(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var S=r(3463),k=r.n(S);function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function E(e,t){if(e){if("string"==typeof e)return P(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(e,t):void 0}}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,s,a=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(a.push(n.value),a.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return a}}(e,t)||E(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var C={defaultNS:"common",errorStackTraceLimit:0,i18n:{defaultLocale:"en",locales:["en"]},get initImmediate(){return"undefined"!=typeof window},get initAsync(){return"undefined"!=typeof window},interpolation:{escapeValue:!1},load:"currentOnly",localeExtension:"json",localePath:"./public/locales",localeStructure:"{{lng}}/{{ns}}",react:{useSuspense:!1},reloadOnPrerender:!1,serializeConfig:!0,use:[]},T="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,L=["i18n"],A=["i18n"];function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function M(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var N=["backend","detection"],_=function(e){if("string"!=typeof(null==e?void 0:e.lng))throw Error("config.lng was not passed into createConfig");var t,r,n,i=e.i18n,o=j(e,L),s=C.i18n,a=M(M(M(M({},j(C,A)),o),s),i),l=a.defaultNS,u=a.lng,c=a.localeExtension,d=a.localePath,h=a.nonExplicitSupportedLngs,p=a.locales.filter(function(e){return"default"!==e});if("cimode"===u)return a;if(void 0===a.fallbackLng&&(a.fallbackLng=a.defaultLocale,"default"===a.fallbackLng)){var f=O(p,1);a.fallbackLng=f[0]}var m=null==e||null===(t=e.interpolation)||void 0===t?void 0:t.prefix,g=null==e||null===(r=e.interpolation)||void 0===r?void 0:r.suffix,y=null!=m?m:"{{",v=null!=g?g:"}}";"string"!=typeof(null==e?void 0:e.localeStructure)&&(m||g)&&(a.localeStructure="".concat(y,"lng").concat(v,"/").concat(y,"ns").concat(v));var b=a.fallbackLng,w=a.localeStructure;if(h){var S=function(e,t){var r=O(t.split("-"),1)[0];return e[t]=[r],e};if("string"==typeof b)a.fallbackLng=a.locales.filter(function(e){return e.includes("-")}).reduce(S,{default:[b]});else if(Array.isArray(b))a.fallbackLng=a.locales.filter(function(e){return e.includes("-")}).reduce(S,{default:b});else if("object"===x(b))a.fallbackLng=Object.entries(a.fallbackLng).reduce(function(e,t){var r,n=O(t,2),i=n[0],o=n[1];return e[i]=i.includes("-")?(r=[i.split("-")[0]].concat(function(e){if(Array.isArray(e))return P(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||E(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),Array.from(new Set(r))):o,e},b);else if("function"==typeof b)throw Error("If nonExplicitSupportedLngs is true, no functions are allowed for fallbackLng")}return(null==e||null===(n=e.use)||void 0===n?void 0:n.some(function(e){return"backend"===e.type}))||("string"==typeof d?a.backend={addPath:"".concat(d,"/").concat(w,".missing.").concat(c),loadPath:"".concat(d,"/").concat(w,".").concat(c)}:"function"!=typeof d||(a.backend={addPath:function(e,t){return d(e,t,!0)},loadPath:function(e,t){return d(e,t,!1)}})),"string"==typeof a.ns||Array.isArray(a.ns)||(a.ns=[l]),N.forEach(function(t){e[t]&&(a[t]=M(M({},a[t]),e[t]))}),a};let D=e=>"string"==typeof e,V=()=>{let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.resolve=e,r.reject=t,r},$=e=>null==e?"":""+e,F=(e,t,r)=>{e.forEach(e=>{t[e]&&(r[e]=t[e])})},I=/###/g,B=e=>e&&e.indexOf("###")>-1?e.replace(I,"."):e,U=e=>!e||D(e),W=(e,t,r)=>{let n=D(t)?t.split("."):t,i=0;for(;i<n.length-1;){if(U(e))return{};let t=B(n[i]);!e[t]&&r&&(e[t]=new r),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++i}return U(e)?{}:{obj:e,k:B(n[i])}},z=(e,t,r)=>{let{obj:n,k:i}=W(e,t,Object);if(void 0!==n||1===t.length){n[i]=r;return}let o=t[t.length-1],s=t.slice(0,t.length-1),a=W(e,s,Object);for(;void 0===a.obj&&s.length;)o=`${s[s.length-1]}.${o}`,(a=W(e,s=s.slice(0,s.length-1),Object))&&a.obj&&void 0!==a.obj[`${a.k}.${o}`]&&(a.obj=void 0);a.obj[`${a.k}.${o}`]=r},H=(e,t,r,n)=>{let{obj:i,k:o}=W(e,t,Object);i[o]=i[o]||[],i[o].push(r)},K=(e,t)=>{let{obj:r,k:n}=W(e,t);if(r)return r[n]},Z=(e,t,r)=>{let n=K(e,r);return void 0!==n?n:K(t,r)},G=(e,t,r)=>{for(let n in t)"__proto__"!==n&&"constructor"!==n&&(n in e?D(e[n])||e[n]instanceof String||D(t[n])||t[n]instanceof String?r&&(e[n]=t[n]):G(e[n],t[n],r):e[n]=t[n]);return e},q=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let J=e=>D(e)?e.replace(/[&<>"'\/]/g,e=>Y[e]):e;class X{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}let Q=[" ",",","?","!",";"],ee=new X(20),et=(e,t,r)=>{t=t||"",r=r||"";let n=Q.filter(e=>0>t.indexOf(e)&&0>r.indexOf(e));if(0===n.length)return!0;let i=ee.getRegExp(`(${n.map(e=>"?"===e?"\\?":e).join("|")})`),o=!i.test(e);if(!o){let t=e.indexOf(r);t>0&&!i.test(e.substring(0,t))&&(o=!0)}return o},er=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let n=t.split(r),i=e;for(let e=0;e<n.length;){let t;if(!i||"object"!=typeof i)return;let o="";for(let s=e;s<n.length;++s)if(s!==e&&(o+=r),o+=n[s],void 0!==(t=i[o])){if(["string","number","boolean"].indexOf(typeof t)>-1&&s<n.length-1)continue;e+=s-e+1;break}i=t}return i},en=e=>e&&e.replace("_","-"),ei={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class eo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||ei,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,n){return n&&!this.debug?null:(D(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new eo(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new eo(this.logger,e)}}var es=new eo;class ea{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let r=this.observers[e].get(t)||0;this.observers[e].set(t,r+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];if(this.observers[e]){let t=Array.from(this.observers[e].entries());t.forEach(e=>{let[t,n]=e;for(let e=0;e<n;e++)t(...r)})}if(this.observers["*"]){let t=Array.from(this.observers["*"].entries());t.forEach(t=>{let[n,i]=t;for(let t=0;t<i;t++)n.apply(n,[e,...r])})}}}class el extends ea{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r){let n,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,s=void 0!==i.ignoreJSONStructure?i.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?n=e.split("."):(n=[e,t],r&&(Array.isArray(r)?n.push(...r):D(r)&&o?n.push(...r.split(o)):n.push(r)));let a=K(this.data,n);return(!a&&!t&&!r&&e.indexOf(".")>-1&&(e=n[0],t=n[1],r=n.slice(2).join(".")),!a&&s&&D(r))?er(this.data&&this.data[e]&&this.data[e][t],r,o):a}addResource(e,t,r,n){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},o=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,s=[e,t];r&&(s=s.concat(o?r.split(o):r)),e.indexOf(".")>-1&&(s=e.split("."),n=t,t=s[1]),this.addNamespaces(t),z(this.data,s,n),i.silent||this.emit("added",e,t,r,n)}addResources(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let n in r)(D(r[n])||Array.isArray(r[n]))&&this.addResource(e,t,n,r[n],{silent:!0});n.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,n,i){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},s=[e,t];e.indexOf(".")>-1&&(s=e.split("."),n=r,r=t,t=s[1]),this.addNamespaces(t);let a=K(this.data,s)||{};o.skipCopy||(r=JSON.parse(JSON.stringify(r))),n?G(a,r,i):a={...a,...r},z(this.data,s,a),o.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e),r=t&&Object.keys(t)||[];return!!r.find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var eu={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,n,i){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,r,n,i))}),t}};let ec={};class ed extends ea{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),F(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=es.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let r=this.resolve(e,t);return r&&void 0!==r.res}extractFromKey(e,t){let r=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===r&&(r=":");let n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,i=t.ns||this.options.defaultNS||[],o=r&&e.indexOf(r)>-1,s=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!et(e,r,n);if(o&&!s){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:D(i)?[i]:i};let o=e.split(r);(r!==n||r===n&&this.options.ns.indexOf(o[0])>-1)&&(i=o.shift()),e=o.join(n)}return{key:e,namespaces:D(i)?[i]:i}}translate(e,t,r){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let n=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:s}=this.extractFromKey(e[e.length-1],t),a=s[s.length-1],l=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&"cimode"===l.toLowerCase()){if(u){let e=t.nsSeparator||this.options.nsSeparator;return n?{res:`${a}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:a,usedParams:this.getUsedParamsDetails(t)}:`${a}${e}${o}`}return n?{res:o,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:a,usedParams:this.getUsedParamsDetails(t)}:o}let c=this.resolve(e,t),d=c&&c.res,h=c&&c.usedKey||o,p=c&&c.exactUsedKey||o,f=Object.prototype.toString.apply(d),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,g=!this.i18nFormat||this.i18nFormat.handleAsObject,y=!D(d)&&"boolean"!=typeof d&&"number"!=typeof d;if(g&&d&&y&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(D(m)&&Array.isArray(d))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(h,d,{...t,ns:s}):`key '${o} (${this.language})' returned an object instead of string.`;return n?(c.res=e,c.usedParams=this.getUsedParamsDetails(t),c):e}if(i){let e=Array.isArray(d),r=e?[]:{},n=e?p:h;for(let e in d)if(Object.prototype.hasOwnProperty.call(d,e)){let o=`${n}${i}${e}`;r[e]=this.translate(o,{...t,joinArrays:!1,ns:s}),r[e]===o&&(r[e]=d[e])}d=r}}else if(g&&D(m)&&Array.isArray(d))(d=d.join(m))&&(d=this.extendTranslation(d,e,t,r));else{let n=!1,s=!1,u=void 0!==t.count&&!D(t.count),h=ed.hasDefaultValue(t),p=u?this.pluralResolver.getSuffix(l,t.count,t):"",f=t.ordinal&&u?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",m=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),g=m&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${p}`]||t[`defaultValue${f}`]||t.defaultValue;!this.isValidLookup(d)&&h&&(n=!0,d=g),this.isValidLookup(d)||(s=!0,d=o);let y=t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,v=y&&s?void 0:d,b=h&&g!==d&&this.options.updateMissing;if(s||n||b){if(this.logger.log(b?"updateKey":"missingKey",l,a,o,b?g:d),i){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],r=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&r&&r[0])for(let t=0;t<r.length;t++)e.push(r[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let n=(e,r,n)=>{let i=h&&n!==d?n:v;this.options.missingKeyHandler?this.options.missingKeyHandler(e,a,r,i,b,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,a,r,i,b,t),this.emit("missingKey",e,a,r,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach(e=>{let r=this.pluralResolver.getSuffixes(e,t);m&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>r.indexOf(`${this.options.pluralSeparator}zero`)&&r.push(`${this.options.pluralSeparator}zero`),r.forEach(r=>{n([e],o+r,t[`defaultValue${r}`]||g)})}):n(e,o,g))}d=this.extendTranslation(d,e,t,c,r),s&&d===o&&this.options.appendNamespaceToMissingKey&&(d=`${a}:${o}`),(s||n)&&this.options.parseMissingKeyHandler&&(d="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${a}:${o}`:o,n?d:void 0):this.options.parseMissingKeyHandler(d))}return n?(c.res=d,c.usedParams=this.getUsedParamsDetails(t),c):d}extendTranslation(e,t,r,n,i){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!r.skipInterpolation){let s;r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});let a=D(e)&&(r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(a){let t=e.match(this.interpolator.nestingRegexp);s=t&&t.length}let l=r.replace&&!D(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,r.lng||this.language||n.usedLng,r),a){let t=e.match(this.interpolator.nestingRegexp),n=t&&t.length;s<n&&(r.nest=!1)}!r.lng&&"v1"!==this.options.compatibilityAPI&&n&&n.res&&(r.lng=this.language||n.usedLng),!1!==r.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,n=Array(e),s=0;s<e;s++)n[s]=arguments[s];return i&&i[0]===n[0]&&!r.context?(o.logger.warn(`It seems you are nesting recursively key: ${n[0]} in key: ${t[0]}`),null):o.translate(...n,t)},r)),r.interpolation&&this.interpolator.reset()}let s=r.postProcess||this.options.postProcess,a=D(s)?[s]:s;return null!=e&&a&&a.length&&!1!==r.applyPostProcessor&&(e=eu.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let t,r,n,i,o,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return D(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let a=this.extractFromKey(e,s),l=a.key;r=l;let u=a.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let c=void 0!==s.count&&!D(s.count),d=c&&!s.ordinal&&0===s.count&&this.pluralResolver.shouldUseIntlApi(),h=void 0!==s.context&&(D(s.context)||"number"==typeof s.context)&&""!==s.context,p=s.lngs?s.lngs:this.languageUtils.toResolveHierarchy(s.lng||this.language,s.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(o=e,!ec[`${p[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(ec[`${p[0]}-${e}`]=!0,this.logger.warn(`key "${r}" for languages "${p.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(r=>{let o;if(this.isValidLookup(t))return;i=r;let a=[l];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(a,l,r,e,s);else{let e;c&&(e=this.pluralResolver.getSuffix(r,s.count,s));let t=`${this.options.pluralSeparator}zero`,n=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(a.push(l+e),s.ordinal&&0===e.indexOf(n)&&a.push(l+e.replace(n,this.options.pluralSeparator)),d&&a.push(l+t)),h){let r=`${l}${this.options.contextSeparator}${s.context}`;a.push(r),c&&(a.push(r+e),s.ordinal&&0===e.indexOf(n)&&a.push(r+e.replace(n,this.options.pluralSeparator)),d&&a.push(r+t))}}for(;o=a.pop();)this.isValidLookup(t)||(n=o,t=this.getResource(r,e,o,s))}))})}),{res:t,usedKey:r,exactUsedKey:n,usedLng:i,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,r,n):this.resourceStore.getResource(e,t,r,n)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!D(e.replace),r=t?e.replace:e;if(t&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!t)for(let e of(r={...r},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete r[e];return r}static hasDefaultValue(e){let t="defaultValue";for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&void 0!==e[r])return!0;return!1}}let eh=e=>e.charAt(0).toUpperCase()+e.slice(1);class ep{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=es.create("languageUtils")}getScriptPartFromCode(e){if(!(e=en(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=en(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(D(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],r=e.split("-");return this.options.lowerCaseLng?r=r.map(e=>e.toLowerCase()):2===r.length?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=eh(r[1].toLowerCase()))):3===r.length&&(r[0]=r[0].toLowerCase(),2===r[1].length&&(r[1]=r[1].toUpperCase()),"sgn"!==r[0]&&2===r[2].length&&(r[2]=r[2].toUpperCase()),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=eh(r[1].toLowerCase())),t.indexOf(r[2].toLowerCase())>-1&&(r[2]=eh(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let r=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(r))&&(t=r)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>{if(e===r||!(0>e.indexOf("-")&&0>r.indexOf("-"))&&(e.indexOf("-")>0&&0>r.indexOf("-")&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),D(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){let r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),n=[],i=e=>{e&&(this.isSupportedCode(e)?n.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return D(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&i(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&i(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&i(this.getLanguagePartFromCode(e))):D(e)&&i(this.formatLanguageCode(e)),r.forEach(e=>{0>n.indexOf(e)&&i(this.formatLanguageCode(e))}),n}}let ef=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],em={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},eg=["v1","v2","v3"],ey=["v4"],ev={zero:0,one:1,two:2,few:3,many:4,other:5},eb=()=>{let e={};return ef.forEach(t=>{t.lngs.forEach(r=>{e[r]={numbers:t.nr,plurals:em[t.fc]}})}),e};class ex{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=es.create("pluralResolver"),(!this.options.compatibilityJSON||ey.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=eb(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let r;let n=en("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:n,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];try{r=new Intl.PluralRules(n,{type:i})}catch(i){if(!e.match(/-|_/))return;let n=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(n,t)}return this.pluralRulesCache[o]=r,r}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,r).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((e,t)=>ev[e]-ev[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):r.numbers.map(r=>this.getSuffix(e,r,t)):[]}getSuffix(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=this.getRule(e,r);return n?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:this.getSuffixRetroCompatible(n,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let r=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),n=e.numbers[r];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===n?n="plural":1===n&&(n=""));let i=()=>this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString();return"v1"===this.options.compatibilityJSON?1===n?"":"number"==typeof n?`_plural_${n.toString()}`:i():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?i():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!eg.includes(this.options.compatibilityJSON)}}let ew=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=Z(e,t,r);return!o&&i&&D(r)&&void 0===(o=er(e,r,n))&&(o=er(t,r,n)),o},eS=e=>e.replace(/\$/g,"$$$$");class ek{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=es.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:r,useRawValueToEscape:n,prefix:i,prefixEscaped:o,suffix:s,suffixEscaped:a,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:h,nestingSuffix:p,nestingSuffixEscaped:f,nestingOptionsSeparator:m,maxReplaces:g,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:J,this.escapeValue=void 0===r||r,this.useRawValueToEscape=void 0!==n&&n,this.prefix=i?q(i):o||"{{",this.suffix=s?q(s):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?q(d):h||q("$t("),this.nestingSuffix=p?q(p):f||q(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=g||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,n){let i,o,s;let a=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(0>e.indexOf(this.formatSeparator)){let i=ew(t,a,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(i,void 0,r,{...n,...t,interpolationkey:e}):i}let i=e.split(this.formatSeparator),o=i.shift().trim(),s=i.join(this.formatSeparator).trim();return this.format(ew(t,a,o,this.options.keySeparator,this.options.ignoreJSONStructure),s,r,{...n,...t,interpolationkey:o})};this.resetRegExp();let u=n&&n.missingInterpolationHandler||this.options.missingInterpolationHandler,c=n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,d=[{regex:this.regexpUnescape,safeValue:e=>eS(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?eS(this.escape(e)):eS(e)}];return d.forEach(t=>{for(s=0;i=t.regex.exec(e);){let r=i[1].trim();if(void 0===(o=l(r))){if("function"==typeof u){let t=u(e,i,n);o=D(t)?t:""}else if(n&&Object.prototype.hasOwnProperty.call(n,r))o="";else if(c){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${r} for interpolating ${e}`),o=""}else D(o)||this.useRawValueToEscape||(o=$(o));let a=t.safeValue(o);if(e=e.replace(i[0],a),c?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=i[0].length):t.regex.lastIndex=0,++s>=this.maxReplaces)break}}),e}nest(e,t){let r,n,i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=(e,t)=>{let r=this.nestingOptionsSeparator;if(0>e.indexOf(r))return e;let n=e.split(RegExp(`${r}[ ]*{`)),o=`{${n[1]}`;e=n[0],o=this.interpolate(o,i);let s=o.match(/'/g),a=o.match(/"/g);(s&&s.length%2==0&&!a||a.length%2!=0)&&(o=o.replace(/'/g,'"'));try{i=JSON.parse(o),t&&(i={...t,...i})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${r}${o}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,e};for(;r=this.nestingRegexp.exec(e);){let a=[];(i=(i={...o}).replace&&!D(i.replace)?i.replace:i).applyPostProcessor=!1,delete i.defaultValue;let l=!1;if(-1!==r[0].indexOf(this.formatSeparator)&&!/{.*}/.test(r[1])){let e=r[1].split(this.formatSeparator).map(e=>e.trim());r[1]=e.shift(),a=e,l=!0}if((n=t(s.call(this,r[1].trim(),i),i))&&r[0]===e&&!D(n))return n;D(n)||(n=$(n)),n||(this.logger.warn(`missed to resolve ${r[1]} for nesting ${e}`),n=""),l&&(n=a.reduce((e,t)=>this.format(e,t,o.lng,{...o,interpolationkey:r[1].trim()}),n.trim())),e=e.replace(r[0],n),this.regexp.lastIndex=0}return e}}let eP=e=>{let t=e.toLowerCase().trim(),r={};if(e.indexOf("(")>-1){let n=e.split("(");t=n[0].toLowerCase().trim();let i=n[1].substring(0,n[1].length-1);if("currency"===t&&0>i.indexOf(":"))r.currency||(r.currency=i.trim());else if("relativetime"===t&&0>i.indexOf(":"))r.range||(r.range=i.trim());else{let e=i.split(";");e.forEach(e=>{if(e){let[t,...n]=e.split(":"),i=n.join(":").trim().replace(/^'+|'+$/g,""),o=t.trim();r[o]||(r[o]=i),"false"===i&&(r[o]=!1),"true"===i&&(r[o]=!0),isNaN(i)||(r[o]=parseInt(i,10))}})}}return{formatName:t,formatOptions:r}},eE=e=>{let t={};return(r,n,i)=>{let o=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(o={...o,[i.interpolationkey]:void 0});let s=n+JSON.stringify(o),a=t[s];return a||(a=e(en(n),i),t[s]=a),a(r)}};class eO{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=es.create("formatter"),this.options=e,this.formats={number:eE((e,t)=>{let r=new Intl.NumberFormat(e,{...t});return e=>r.format(e)}),currency:eE((e,t)=>{let r=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>r.format(e)}),datetime:eE((e,t)=>{let r=new Intl.DateTimeFormat(e,{...t});return e=>r.format(e)}),relativetime:eE((e,t)=>{let r=new Intl.RelativeTimeFormat(e,{...t});return e=>r.format(e,t.range||"day")}),list:eE((e,t)=>{let r=new Intl.ListFormat(e,{...t});return e=>r.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=eE(t)}format(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&0>i[0].indexOf(")")&&i.find(e=>e.indexOf(")")>-1)){let e=i.findIndex(e=>e.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,e)].join(this.formatSeparator)}let o=i.reduce((e,t)=>{let{formatName:i,formatOptions:o}=eP(t);if(this.formats[i]){let t=e;try{let s=n&&n.formatParams&&n.formatParams[n.interpolationkey]||{},a=s.locale||s.lng||n.locale||n.lng||r;t=this.formats[i](e,a,{...o,...n,...s})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${i}`),e},e);return o}}let ej=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class eC extends ea{constructor(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=n,this.logger=es.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,n.backend,n)}queueLoad(e,t,r,n){let i={},o={},s={},a={};return e.forEach(e=>{let n=!0;t.forEach(t=>{let s=`${e}|${t}`;!r.reload&&this.store.hasResourceBundle(e,t)?this.state[s]=2:this.state[s]<0||(1===this.state[s]?void 0===o[s]&&(o[s]=!0):(this.state[s]=1,n=!1,void 0===o[s]&&(o[s]=!0),void 0===i[s]&&(i[s]=!0),void 0===a[t]&&(a[t]=!0)))}),n||(s[e]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(s),toLoadNamespaces:Object.keys(a)}}loaded(e,t,r){let n=e.split("|"),i=n[0],o=n[1];t&&this.emit("failedLoading",i,o,t),!t&&r&&this.store.addResourceBundle(i,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);let s={};this.queue.forEach(r=>{H(r.loaded,[i],o),ej(r,e),t&&r.errors.push(t),0!==r.pendingCount||r.done||(Object.keys(r.loaded).forEach(e=>{s[e]||(s[e]={});let t=r.loaded[e];t.length&&t.forEach(t=>{void 0===s[e][t]&&(s[e][t]=!0)})}),r.done=!0,r.errors.length?r.callback(r.errors):r.callback())}),this.emit("loaded",s),this.queue=this.queue.filter(e=>!e.done)}read(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:n,wait:i,callback:o});return}this.readingCalls++;let s=(s,a)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(s&&a&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,n+1,2*i,o)},i);return}o(s,a)},a=this.backend[r].bind(this.backend);if(2===a.length){try{let r=a(e,t);r&&"function"==typeof r.then?r.then(e=>s(null,e)).catch(s):s(null,r)}catch(e){s(e)}return}return a(e,t,s)}prepareLoading(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();D(e)&&(e=this.languageUtils.toResolveHierarchy(e)),D(t)&&(t=[t]);let i=this.queueLoad(e,t,r,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.split("|"),n=r[0],i=r[1];this.read(n,i,"read",void 0,void 0,(r,o)=>{r&&this.logger.warn(`${t}loading namespace ${i} for language ${n} failed`,r),!r&&o&&this.logger.log(`${t}loaded namespace ${i} for language ${n}`,o),this.loaded(e,r,o)})}saveMissing(e,t,r,n,i){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=r&&""!==r){if(this.backend&&this.backend.create){let a={...o,isUpdate:i},l=this.backend.create.bind(this.backend);if(l.length<6)try{let i;(i=5===l.length?l(e,t,r,n,a):l(e,t,r,n))&&"function"==typeof i.then?i.then(e=>s(null,e)).catch(s):s(null,i)}catch(e){s(e)}else l(e,t,r,n,s,a)}e&&e[0]&&this.store.addResource(e[0],t,r,n)}}}let eT=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),D(e[1])&&(t.defaultValue=e[1]),D(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let r=e[3]||e[2];Object.keys(r).forEach(e=>{t[e]=r[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),eL=e=>(D(e.ns)&&(e.ns=[e.ns]),D(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),D(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),eA=()=>{},eR=e=>{let t=Object.getOwnPropertyNames(Object.getPrototypeOf(e));t.forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class eM extends ea{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=eL(e),this.services={},this.logger=es,this.modules={external:[]},eR(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(r=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(D(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let n=eT();this.options={...n,...this.options,...eL(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let i=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?es.init(i(this.modules.logger),this.options):es.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=eO);let r=new ep(this.options);this.store=new el(this.options.resources,this.options);let o=this.services;o.logger=es,o.resourceStore=this.store,o.languageUtils=r,o.pluralResolver=new ex(r,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(o.formatter=i(t),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new ek(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new eC(i(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];e.emit(t,...n)}),this.modules.languageDetector&&(o.languageDetector=i(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=i(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new ed(this.services,this.options),this.translator.on("*",function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];e.emit(t,...n)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,r||(r=eA),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let o=V(),s=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(t),r(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?s():setTimeout(s,0),o}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eA,r=t,n=D(e)?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return r();let e=[],t=t=>{if(!t||"cimode"===t)return;let r=this.services.languageUtils.toResolveHierarchy(t);r.forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};if(n)t(n);else{let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.forEach(e=>t(e))}this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),r(e)})}else r(null)}reloadResources(e,t,r){let n=V();return"function"==typeof e&&(r=e,e=void 0),"function"==typeof t&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=eA),this.services.backendConnector.reload(e,t,e=>{n.resolve(),r(e)}),n}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&eu.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var r=this;this.isLanguageChangingTo=e;let n=V();this.emit("languageChanging",e);let i=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(e,o)=>{o?(i(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,n.resolve(function(){return r.t(...arguments)}),t&&t(e,function(){return r.t(...arguments)})},s=t=>{e||t||!this.services.languageDetector||(t=[]);let r=D(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);r&&(this.language||i(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(r)),this.loadResources(r,e=>{o(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(s):this.services.languageDetector.detect(s):s(e):s(this.services.languageDetector.detect()),n}getFixedT(e,t,r){var n=this;let i=function(e,t){let o,s;if("object"!=typeof t){for(var a=arguments.length,l=Array(a>2?a-2:0),u=2;u<a;u++)l[u-2]=arguments[u];o=n.options.overloadTranslationOptionHandler([e,t].concat(l))}else o={...t};o.lng=o.lng||i.lng,o.lngs=o.lngs||i.lngs,o.ns=o.ns||i.ns,""!==o.keyPrefix&&(o.keyPrefix=o.keyPrefix||r||i.keyPrefix);let c=n.options.keySeparator||".";return s=o.keyPrefix&&Array.isArray(e)?e.map(e=>`${o.keyPrefix}${c}${e}`):o.keyPrefix?`${o.keyPrefix}${c}${e}`:e,n.t(s,o)};return D(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=r,i}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let r=t.lng||this.resolvedLanguage||this.languages[0],n=!!this.options&&this.options.fallbackLng,i=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;let o=(e,t)=>{let r=this.services.backendConnector.state[`${e}|${t}`];return -1===r||0===r||2===r};if(t.precheck){let e=t.precheck(this,o);if(void 0!==e)return e}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(r,e)&&(!n||o(i,e)))}loadNamespaces(e,t){let r=V();return this.options.ns?(D(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){let r=V();D(e)&&(e=[e]);let n=this.options.preload||[],i=e.filter(e=>0>n.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return i.length?(this.options.preload=n.concat(i),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";let t=this.services&&this.services.languageUtils||new ep(eT());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new eM(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eA,r=e.forkResourceStore;r&&delete e.forkResourceStore;let n={...this.options,...e,isClone:!0},i=new eM(n);return(void 0!==e.debug||void 0!==e.prefix)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(e=>{i[e]=this[e]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r&&(i.store=new el(this.store.data,n),i.services.resourceStore=i.store),i.translator=new ed(i.services,n),i.translator.on("*",function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];i.emit(e,...r)}),i.init(n,t),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let eN=eM.createInstance();eN.createInstance=eM.createInstance,eN.createInstance,eN.dir,eN.init,eN.loadResources,eN.reloadResources,eN.use,eN.changeLanguage,eN.getFixedT,eN.t,eN.exists,eN.setDefaultNamespace,eN.hasLoadedNamespace,eN.loadNamespaces,eN.loadLanguages;var e_=function(e){void 0===e.ns&&(e.ns=[]);var t,r,n=eN.createInstance(e);return n.isInitialized?t=Promise.resolve(eN.t):(null==e||null===(r=e.use)||void 0===r||r.forEach(function(e){return n.use(e)}),"function"==typeof e.onPreInitI18next&&e.onPreInitI18next(n),t=n.init(e)),{i18n:n,initPromise:t}},eD=i.createElement;function eV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eV(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eF=function(e,t){if(t&&e.isInitialized)for(var r=0,n=Object.keys(t);r<n.length;r++)for(var i=n[r],o=0,s=Object.keys(t[i]);o<s.length;o++){var a,l=s[o];null!=e&&null!==(a=e.store)&&void 0!==a&&a.data&&e.store.data[i]&&e.store.data[i][l]||e.addResourceBundle(i,l,t[i][l],!0,!0)}},eI=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return k()(function(r){var n,o,s=(r.pageProps||{})._nextI18Next,a=null!==(n=null==s?void 0:s.initialLocale)&&void 0!==n?n:null==r||null===(o=r.router)||void 0===o?void 0:o.locale,l=null==s?void 0:s.ns,u=(0,i.useRef)(null),c=(0,i.useMemo)(function(){if(!s&&!t)return null;var e,r=null!=t?t:null==s?void 0:s.userConfig;if(!r)throw Error("appWithTranslation was called without a next-i18next config");if(!(null!=r&&r.i18n))throw Error("appWithTranslation was called without config.i18n");if(!(null!=r&&null!==(e=r.i18n)&&void 0!==e&&e.defaultLocale))throw Error("config.i18n does not include a defaultLocale property");var n=(s||{}).initialI18nStore,i=null!=t&&t.resources?t.resources:n;a||(a=r.i18n.defaultLocale);var o=u.current;return o?eF(o,i):(eF(o=e_(e$(e$(e$({},_(e$(e$({},r),{},{lng:a}))),{},{lng:a},l&&{ns:l}),{},{resources:i})).i18n,i),u.current=o),o},[s,a,l]);return T(function(){c&&a&&c.changeLanguage(a)},[c,a]),null!==c?eD(v,{i18n:c},eD(e,r)):eD(e,b({key:a},r))},e)}},3542:function(e,t,r){"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(2351)},6570:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(6769)}])},6769:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return B}});var n=r(2322),i=r(3592),o=r.n(i),s=r(9288),a=r.n(s),l=r(6526),u=r.n(l),c=r(3785),d=r.n(c),h=r(3340),p=r.n(h),f=r(34),m=r(2784);let g=["light","dark"],y="(prefers-color-scheme: dark)",v="undefined"==typeof window,b=(0,m.createContext)(void 0),x=e=>(0,m.useContext)(b)?m.createElement(m.Fragment,null,e.children):m.createElement(S,e),w=["light","dark"],S=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:n=!0,storageKey:i="theme",themes:o=w,defaultTheme:s=r?"system":"light",attribute:a="data-theme",value:l,children:u,nonce:c})=>{let[d,h]=(0,m.useState)(()=>P(i,s)),[p,f]=(0,m.useState)(()=>P(i)),v=l?Object.values(l):o,x=(0,m.useCallback)(e=>{let i=e;if(!i)return;"system"===e&&r&&(i=O());let o=l?l[i]:i,u=t?E():null,c=document.documentElement;if("class"===a?(c.classList.remove(...v),o&&c.classList.add(o)):o?c.setAttribute(a,o):c.removeAttribute(a),n){let e=g.includes(s)?s:null,t=g.includes(i)?i:e;c.style.colorScheme=t}null==u||u()},[]),S=(0,m.useCallback)(e=>{h(e);try{localStorage.setItem(i,e)}catch(e){}},[e]),j=(0,m.useCallback)(t=>{let n=O(t);f(n),"system"===d&&r&&!e&&x("system")},[d,e]);(0,m.useEffect)(()=>{let e=window.matchMedia(y);return e.addListener(j),j(e),()=>e.removeListener(j)},[j]),(0,m.useEffect)(()=>{let e=e=>{e.key===i&&S(e.newValue||s)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),(0,m.useEffect)(()=>{x(null!=e?e:d)},[e,d]);let C=(0,m.useMemo)(()=>({theme:d,setTheme:S,forcedTheme:e,resolvedTheme:"system"===d?p:d,themes:r?[...o,"system"]:o,systemTheme:r?p:void 0}),[d,S,e,p,r,o]);return m.createElement(b.Provider,{value:C},m.createElement(k,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:r,enableColorScheme:n,storageKey:i,themes:o,defaultTheme:s,attribute:a,value:l,children:u,attrs:v,nonce:c}),u)},k=(0,m.memo)(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:n,enableColorScheme:i,defaultTheme:o,value:s,attrs:a,nonce:l})=>{let u="system"===o,c="class"===r?`var d=document.documentElement,c=d.classList;c.remove(${a.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,d=i?g.includes(o)&&o?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${o}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",h=(e,t=!1,n=!0)=>{let o=s?s[e]:e,a=t?e+"|| ''":`'${o}'`,l="";return i&&n&&!t&&g.includes(e)&&(l+=`d.style.colorScheme = '${e}';`),"class"===r?l+=t||o?`c.add(${a})`:"null":o&&(l+=`d[s](n,${a})`),l},p=e?`!function(){${c}${h(e)}}()`:n?`!function(){try{${c}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${y}',m=window.matchMedia(t);if(m.media!==t||m.matches){${h("dark")}}else{${h("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}${u?"":"else{"+h(o,!1,!1)+"}"}${d}}catch(e){}}()`:`!function(){try{${c}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}else{${h(o,!1,!1)};}${d}}catch(t){}}();`;return m.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:p}})},()=>!0),P=(e,t)=>{let r;if(!v){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},E=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},O=e=>(e||(e=window.matchMedia(y)),e.matches?"dark":"light");var j=r(2202),C=r(5632);class T extends m.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){var r,n,i,o,s,a;let l=(null===(r=e.message)||void 0===r?void 0:r.includes("Frame with ID"))||(null===(n=e.message)||void 0===n?void 0:n.includes("Could not establish connection"))||(null===(i=e.message)||void 0===i?void 0:i.includes("MetaMask"))||(null===(o=e.message)||void 0===o?void 0:o.includes("chrome-extension"))||(null===(s=e.message)||void 0===s?void 0:s.includes("contentscript"))||(null===(a=e.message)||void 0===a?void 0:a.includes("serviceWorker"));l||console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){var e,t,r,i,o,s,a,l,u,c,d,h;let p=(null===(t=this.state.error)||void 0===t?void 0:null===(e=t.message)||void 0===e?void 0:e.includes("Frame with ID"))||(null===(i=this.state.error)||void 0===i?void 0:null===(r=i.message)||void 0===r?void 0:r.includes("Could not establish connection"))||(null===(s=this.state.error)||void 0===s?void 0:null===(o=s.message)||void 0===o?void 0:o.includes("MetaMask"))||(null===(l=this.state.error)||void 0===l?void 0:null===(a=l.message)||void 0===a?void 0:a.includes("chrome-extension"))||(null===(c=this.state.error)||void 0===c?void 0:null===(u=c.message)||void 0===u?void 0:u.includes("contentscript"))||(null===(h=this.state.error)||void 0===h?void 0:null===(d=h.message)||void 0===d?void 0:d.includes("serviceWorker"));return p?this.props.children:this.props.fallback||(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"We're sorry, but something unexpected happened. Please refresh the page."}),(0,n.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"btn-primary",children:"Refresh Page"})]})})}return this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var L=r(3848),A=e=>{let{children:t,session:r}=e;return(0,n.jsx)(L.SessionProvider,{session:r,refetchInterval:300,refetchOnWindowFocus:!0,children:t})},R=r(1675),M=r(5239),N=r(8508);let _=m.forwardRef(function({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"}))});var D=r(5116),V=r(6763);let $=m.forwardRef(function({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),F=m.forwardRef(function({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});var I=e=>{let{showInProduction:t=!1,position:r="bottom-right"}=e,{switchTheme:i,isGoldTheme:o,isPurpleTheme:s}=(0,R.Fg)(),[a,l]=(0,m.useState)(!1),[u,c]=(0,m.useState)(!1);if((0,m.useEffect)(()=>{c(t)},[t]),!u)return null;let d=e=>{i(e),setTimeout(()=>l(!1),500)};return(0,n.jsxs)("div",{className:"fixed ".concat({"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","top-right":"top-6 right-6","top-left":"top-6 left-6"}[r]," z-[9999] select-none"),children:[(0,n.jsx)(M.M,{children:a&&(0,n.jsxs)(N.E.div,{initial:{opacity:0,y:20,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.9},transition:{duration:.2,ease:"easeOut"},className:"mb-4 p-4 rounded-2xl overflow-hidden",style:{background:"linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)",backdropFilter:"blur(25px)",WebkitBackdropFilter:"blur(25px)",border:"1px solid rgba(255, 255, 255, 0.1)",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.5)"},children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(_,{className:"w-5 h-5 text-white"}),(0,n.jsx)("span",{className:"text-white font-semibold text-sm",children:"Theme Controller"})]}),(0,n.jsx)("button",{onClick:()=>l(!1),className:"text-white/60 hover:text-white transition-colors p-1",children:(0,n.jsx)(D.Z,{className:"w-4 h-4"})})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(N.E.button,{onClick:()=>d("gold"),className:"w-full p-3 rounded-xl transition-all duration-300 group ".concat(o?"ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-500/20 to-orange-500/20":"hover:bg-white/5"),whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",boxShadow:"0 4px 12px rgba(255, 215, 0, 0.3)"}}),o&&(0,n.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,n.jsx)(V.Z,{className:"w-4 h-4 text-yellow-400"})})]}),(0,n.jsxs)("div",{className:"flex-1 text-left",children:[(0,n.jsx)("div",{className:"text-white font-medium text-sm",children:"Gold Premium"}),(0,n.jsx)("div",{className:"text-white/60 text-xs",children:"Luxury & Elegance"})]}),o&&(0,n.jsx)("div",{className:"text-yellow-400",children:(0,n.jsx)($,{className:"w-4 h-4"})})]})}),(0,n.jsx)(N.E.button,{onClick:()=>d("purple"),className:"w-full p-3 rounded-xl transition-all duration-300 group ".concat(s?"ring-2 ring-purple-400 bg-gradient-to-r from-purple-500/20 to-blue-500/20":"hover:bg-white/5"),whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:"0 4px 12px rgba(217, 70, 239, 0.3)"}}),s&&(0,n.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,n.jsx)(V.Z,{className:"w-4 h-4 text-purple-400"})})]}),(0,n.jsxs)("div",{className:"flex-1 text-left",children:[(0,n.jsx)("div",{className:"text-white font-medium text-sm",children:"Purple Dark"}),(0,n.jsx)("div",{className:"text-white/60 text-xs",children:"Modern & Professional"})]}),s&&(0,n.jsx)("div",{className:"text-purple-400",children:(0,n.jsx)($,{className:"w-4 h-4"})})]})})]}),(0,n.jsx)("div",{className:"mt-4 pt-3 border-t border-white/10",children:(0,n.jsxs)("div",{className:"text-white/40 text-xs text-center",children:["Press ",(0,n.jsx)("kbd",{className:"px-1 py-0.5 bg-white/10 rounded text-white/60",children:"Ctrl+Shift+T"})," to toggle"]})})]})}),(0,n.jsxs)(N.E.button,{onClick:()=>l(!a),className:"relative p-3 rounded-full overflow-hidden group",style:{background:o?"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)":"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:o?"0 8px 25px rgba(255, 215, 0, 0.4)":"0 8px 25px rgba(217, 70, 239, 0.4)"},whileHover:{scale:1.1},whileTap:{scale:.95},animate:{rotate:a?180:0},transition:{duration:.3},children:[(0,n.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500",style:{background:"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)",backgroundSize:"200% 100%",animation:"shimmer 2s ease-in-out infinite"}}),(0,n.jsx)("div",{className:"relative z-10",children:a?(0,n.jsx)(F,{className:"w-6 h-6 text-white"}):(0,n.jsx)(_,{className:"w-6 h-6 text-white"})}),(0,n.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white shadow-lg"})]}),(0,n.jsx)(M.M,{children:!a&&(0,n.jsx)(N.E.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:-10},className:"absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 rounded-md text-xs text-white whitespace-nowrap pointer-events-none",style:{background:"rgba(0, 0, 0, 0.8)",backdropFilter:"blur(10px)"},children:o?"Gold Theme":"Purple Theme"})})]})};r(3596);var B=(0,f.Jc)(function(e){let{Component:t,pageProps:{session:r,...i}}=e,s=(0,C.useRouter)(),{locale:l}=s,c="ar"===l;return(0,m.useEffect)(()=>{document.documentElement.dir=c?"rtl":"ltr",document.documentElement.lang=l||"ar"},[l,c]),(0,n.jsx)(T,{children:(0,n.jsx)("div",{className:"".concat(o().variable," ").concat(a().variable," ").concat(u().variable," ").concat(d().variable," ").concat(p().variable," ").concat(c?"font-cairo":"font-sans"),children:(0,n.jsx)(A,{session:r,children:(0,n.jsx)(R.f6,{defaultTheme:"gold",children:(0,n.jsxs)(x,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!1,children:[(0,n.jsx)(t,{...i}),(0,n.jsx)(I,{}),(0,n.jsx)(j.x7,{position:c?"top-left":"top-right",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",direction:c?"rtl":"ltr"}}})]})})})})})})},1675:function(e,t,r){"use strict";r.d(t,{f6:function(){return S},Fg:function(){return k}});var n=r(2322),i=r(2784);let o={name:"gold",displayName:"Gold Premium",colors:{primary:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},secondary:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},accent:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(255, 215, 0, 0.12)",border:"rgba(255, 215, 0, 0.25)",shadow:"0 8px 32px rgba(255, 215, 0, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#FFD700",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:"\n      radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    ",secondary:"\n      radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    ",tertiary:"\n      radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    "},gradients:{primary:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",secondary:"linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)",accent:"linear-gradient(135deg, #DAA520 0%, #B8860B 100%)",text:"linear-gradient(90deg,\n      #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%,\n      #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%,\n      #DAA520 80%, #B8860B 90%, #8B6914 100%)",button:"linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%)",card:"linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(255, 215, 0, 0.1)",md:"0 4px 8px rgba(255, 215, 0, 0.15)",lg:"0 8px 16px rgba(255, 215, 0, 0.2)",xl:"0 12px 24px rgba(255, 215, 0, 0.25)",glass:"0 8px 32px rgba(255, 215, 0, 0.15)",premium:"0 25px 50px rgba(255, 215, 0, 0.3)"},animations:{shimmer:"\n      @keyframes goldShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    ",glow:"\n      @keyframes goldGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }\n      }\n    ",pulse:"\n      @keyframes goldPulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    ",float:"\n      @keyframes goldFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    "}},s={"--theme-primary-50":o.colors.primary[50],"--theme-primary-100":o.colors.primary[100],"--theme-primary-200":o.colors.primary[200],"--theme-primary-300":o.colors.primary[300],"--theme-primary-400":o.colors.primary[400],"--theme-primary-500":o.colors.primary[500],"--theme-primary-600":o.colors.primary[600],"--theme-primary-700":o.colors.primary[700],"--theme-primary-800":o.colors.primary[800],"--theme-primary-900":o.colors.primary[900],"--theme-glass-bg":o.colors.glass.background,"--theme-glass-border":o.colors.glass.border,"--theme-glass-shadow":o.colors.glass.shadow,"--theme-glass-blur":o.colors.glass.backdropBlur,"--theme-bg-primary":o.backgrounds.primary,"--theme-bg-secondary":o.backgrounds.secondary,"--theme-bg-tertiary":o.backgrounds.tertiary||o.backgrounds.secondary,"--theme-text-primary":o.colors.text.primary,"--theme-text-secondary":o.colors.text.secondary,"--theme-text-accent":o.colors.text.accent,"--theme-text-muted":o.colors.text.muted,"--theme-gradient-primary":o.gradients.primary,"--theme-gradient-secondary":o.gradients.secondary,"--theme-gradient-accent":o.gradients.accent,"--theme-gradient-text":o.gradients.text,"--theme-gradient-button":o.gradients.button,"--theme-gradient-card":o.gradients.card,"--theme-shadow-sm":o.shadows.sm,"--theme-shadow-md":o.shadows.md,"--theme-shadow-lg":o.shadows.lg,"--theme-shadow-xl":o.shadows.xl,"--theme-shadow-glass":o.shadows.glass,"--theme-shadow-premium":o.shadows.premium},a={name:"purple",displayName:"Purple Dark",colors:{primary:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},secondary:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},accent:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(217, 70, 239, 0.12)",border:"rgba(217, 70, 239, 0.25)",shadow:"0 8px 32px rgba(217, 70, 239, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#d946ef",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:"\n      radial-gradient(circle at 50% 50%, rgba(217, 70, 239, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    ",secondary:"\n      radial-gradient(ellipse at top, rgba(192, 38, 211, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    ",tertiary:"\n      radial-gradient(ellipse at bottom, rgba(162, 28, 175, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    "},gradients:{primary:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",secondary:"linear-gradient(135deg, #c026d3 0%, #86198f 100%)",accent:"linear-gradient(135deg, #e879f9 0%, #c026d3 100%)",text:"linear-gradient(90deg,\n      #701a75 0%, #86198f 10%, #a21caf 20%, #c026d3 30%,\n      #d946ef 40%, #e879f9 50%, #d946ef 60%, #c026d3 70%,\n      #a21caf 80%, #86198f 90%, #701a75 100%)",button:"linear-gradient(135deg, #d946ef 0%, #a21caf 50%, #86198f 100%)",card:"linear-gradient(135deg, rgba(217, 70, 239, 0.08) 0%, rgba(217, 70, 239, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(217, 70, 239, 0.1)",md:"0 4px 8px rgba(217, 70, 239, 0.15)",lg:"0 8px 16px rgba(217, 70, 239, 0.2)",xl:"0 12px 24px rgba(217, 70, 239, 0.25)",glass:"0 8px 32px rgba(217, 70, 239, 0.15)",premium:"0 25px 50px rgba(217, 70, 239, 0.3)"},animations:{shimmer:"\n      @keyframes purpleShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    ",glow:"\n      @keyframes purpleGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.6); }\n      }\n    ",pulse:"\n      @keyframes purplePulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    ",float:"\n      @keyframes purpleFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    "}},l={"--theme-primary-50":a.colors.primary[50],"--theme-primary-100":a.colors.primary[100],"--theme-primary-200":a.colors.primary[200],"--theme-primary-300":a.colors.primary[300],"--theme-primary-400":a.colors.primary[400],"--theme-primary-500":a.colors.primary[500],"--theme-primary-600":a.colors.primary[600],"--theme-primary-700":a.colors.primary[700],"--theme-primary-800":a.colors.primary[800],"--theme-primary-900":a.colors.primary[900],"--theme-glass-bg":a.colors.glass.background,"--theme-glass-border":a.colors.glass.border,"--theme-glass-shadow":a.colors.glass.shadow,"--theme-glass-blur":a.colors.glass.backdropBlur,"--theme-bg-primary":a.backgrounds.primary,"--theme-bg-secondary":a.backgrounds.secondary,"--theme-bg-tertiary":a.backgrounds.tertiary||a.backgrounds.secondary,"--theme-text-primary":a.colors.text.primary,"--theme-text-secondary":a.colors.text.secondary,"--theme-text-accent":a.colors.text.accent,"--theme-text-muted":a.colors.text.muted,"--theme-gradient-primary":a.gradients.primary,"--theme-gradient-secondary":a.gradients.secondary,"--theme-gradient-accent":a.gradients.accent,"--theme-gradient-text":a.gradients.text,"--theme-gradient-button":a.gradients.button,"--theme-gradient-card":a.gradients.card,"--theme-shadow-sm":a.shadows.sm,"--theme-shadow-md":a.shadows.md,"--theme-shadow-lg":a.shadows.lg,"--theme-shadow-xl":a.shadows.xl,"--theme-shadow-glass":a.shadows.glass,"--theme-shadow-premium":a.shadows.premium},u={gold:o,purple:a},c={gold:s,purple:l},d=e=>u[e],h=e=>c[e],p=e=>{let t=h(e),r=document.documentElement;r.classList.remove("theme-gold","theme-purple"),r.classList.add("theme-".concat(e)),Object.entries(t).forEach(e=>{let[t,n]=e;r.style.setProperty(t,n)})},f=()=>{try{let e=localStorage.getItem("freela-theme");return e&&("gold"===e||"purple"===e)?e:null}catch(e){return null}},m=e=>{try{localStorage.setItem("freela-theme",e)}catch(e){}},g=e=>"theme-".concat(e),y=()=>{if("undefined"==typeof document)return;let e=document.createElement("style");e.textContent="\n    * {\n      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;\n    }\n  ",document.head.appendChild(e),setTimeout(()=>{document.head.removeChild(e)},300)},v=e=>{let t=d(e),r=h(e);return"\n    .theme-".concat(e," {\n      ").concat(Object.entries(r).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r,";")}).join("\n      "),"\n    }\n    \n    /* Theme-specific animations */\n    ").concat(t.animations.shimmer,"\n    ").concat(t.animations.glow,"\n    ").concat(t.animations.pulse,"\n    ").concat(t.animations.float,"\n    \n    /* Theme-specific utilities */\n    .theme-").concat(e," .bg-theme-primary {\n      background: var(--theme-bg-primary);\n    }\n    \n    .theme-").concat(e," .bg-theme-secondary {\n      background: var(--theme-bg-secondary);\n    }\n    \n    .theme-").concat(e," .glass-effect {\n      background: var(--theme-glass-bg);\n      border: 1px solid var(--theme-glass-border);\n      box-shadow: var(--theme-glass-shadow);\n      backdrop-filter: var(--theme-glass-blur);\n      -webkit-backdrop-filter: var(--theme-glass-blur);\n    }\n    \n    .theme-").concat(e," .text-theme-gradient {\n      background: var(--theme-gradient-text);\n      background-size: 300% 100%;\n      -webkit-background-clip: text;\n      background-clip: text;\n      -webkit-text-fill-color: transparent;\n      animation: ").concat(e,"Shimmer 4s ease-in-out infinite;\n    }\n    \n    .theme-").concat(e," .btn-theme-primary {\n      background: var(--theme-gradient-button);\n      box-shadow: var(--theme-shadow-premium);\n      color: white;\n      border: 1px solid var(--theme-glass-border);\n    }\n    \n    .theme-").concat(e," .btn-theme-primary:hover {\n      animation: ").concat(e,"Glow 2s ease-in-out infinite;\n    }\n  ")},b=e=>{if("undefined"==typeof document)return;let t=document.getElementById("theme-".concat(e,"-styles"));t&&t.remove();let r=document.createElement("style");r.id="theme-".concat(e,"-styles"),r.textContent=v(e),document.head.appendChild(r)},x=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"gold",t=f(),r=t||e;return p(r),b(r),r},w=(0,i.createContext)(void 0),S=e=>{let{children:t,defaultTheme:r="gold"}=e,[o,s]=(0,i.useState)(r),[a,l]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=x(r);s(e),l(!0)},[r]);let u=d(o),c=(0,i.useCallback)(e=>{e!==o&&(y(),p(e),b(e),s(e),m(e),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:e}})))},[o]);(0,i.useEffect)(()=>{let e=e=>{if(e.ctrlKey&&e.shiftKey&&"T"===e.key){e.preventDefault();let t="gold"===o?"purple":"gold";c(t)}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[o,c]);let h={currentTheme:u,themeName:o,switchTheme:c,isGoldTheme:"gold"===o,isPurpleTheme:"purple"===o,themeClasses:g(o)};return a?(0,n.jsx)(w.Provider,{value:h,children:(0,n.jsx)("div",{className:"theme-provider ".concat(g(o)),children:t})}):null},k=()=>{let e=(0,i.useContext)(w);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},3596:function(){},3785:function(e){e.exports={style:{fontFamily:"'__Cairo_baae29', '__Cairo_Fallback_baae29'",fontStyle:"normal"},className:"__className_baae29",variable:"__variable_baae29"}},3592:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9288:function(e){e.exports={style:{fontFamily:"'__Noto_Sans_Arabic_386ca1', '__Noto_Sans_Arabic_Fallback_386ca1'",fontStyle:"normal"},className:"__className_386ca1",variable:"__variable_386ca1"}},6526:function(e){e.exports={style:{fontFamily:"'__Poppins_51684b', '__Poppins_Fallback_51684b'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}},3340:function(e){e.exports={style:{fontFamily:"'__Tajawal_a9af04', '__Tajawal_Fallback_a9af04'",fontStyle:"normal"},className:"__className_a9af04",variable:"__variable_a9af04"}},2351:function(e){!function(){var t={229:function(e){var t,r,n,i=e.exports={};function o(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&h())}function h(){if(!u){var e=a(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function f(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new p(e,t)),1!==l.length||u||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},s=!0;try{t[e](o,o.exports,n),s=!1}finally{s&&delete r[e]}return o.exports}n.ab="//";var i=n(229);e.exports=i}()},5632:function(e,t,r){e.exports=r(5123)},6866:function(e,t){"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,s=r?Symbol.for("react.strict_mode"):60108,a=r?Symbol.for("react.profiler"):60114,l=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,d=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,y=r?Symbol.for("react.block"):60121,v=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,x=r?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case c:case d:case o:case a:case s:case p:return e;default:switch(e=e&&e.$$typeof){case u:case h:case g:case m:case l:return e;default:return t}}case i:return t}}}function S(e){return w(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=n,t.ForwardRef=h,t.Fragment=o,t.Lazy=g,t.Memo=m,t.Portal=i,t.Profiler=a,t.StrictMode=s,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||w(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return w(e)===u},t.isContextProvider=function(e){return w(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return w(e)===h},t.isFragment=function(e){return w(e)===o},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===i},t.isProfiler=function(e){return w(e)===a},t.isStrictMode=function(e){return w(e)===s},t.isSuspense=function(e){return w(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===d||e===a||e===s||e===p||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===h||e.$$typeof===v||e.$$typeof===b||e.$$typeof===x||e.$$typeof===y)},t.typeOf=w},8570:function(e,t,r){"use strict";e.exports=r(6866)},4896:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},6459:function(e){e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},5748:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},6314:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},5297:function(e){e.exports=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},1461:function(e){function t(e,t,r,n,i,o,s){try{var a=e[o](s),l=a.value}catch(e){return void r(e)}a.done?t(l):Promise.resolve(l).then(n,i)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise(function(i,o){var s=e.apply(r,n);function a(e){t(s,i,o,a,l,"next",e)}function l(e){t(s,i,o,a,l,"throw",e)}a(void 0)})}},e.exports.__esModule=!0,e.exports.default=e.exports},3100:function(e){e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},3419:function(e,t,r){var n=r(7028),i=r(8560);e.exports=function(e,t,r){if(n())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,t);var s=new(e.bind.apply(e,o));return r&&i(s,r.prototype),s},e.exports.__esModule=!0,e.exports.default=e.exports},8870:function(e,t,r){var n=r(7739);function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}e.exports=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},6290:function(e,t,r){var n=r(7739);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},1147:function(e){function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},8230:function(e,t,r){var n=r(8560);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},1600:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},9549:function(e){e.exports=function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}},e.exports.__esModule=!0,e.exports.default=e.exports},7028:function(e){function t(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e.exports=t=function(){return!!r},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},6193:function(e){e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,s,a=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(a.push(n.value),a.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports},6147:function(e){e.exports=function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},421:function(e,t,r){var n=r(7425).default,i=r(5297);e.exports=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports.default=e.exports},5015:function(e,t,r){var n=r(6688);function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,r,o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function l(e,i,o,s){var a=Object.create((i&&i.prototype instanceof c?i:c).prototype);return n(a,"_invoke",function(e,n,i){var o,s,a,l=0,c=i||[],d=!1,h={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return o=e,s=0,a=t,h.n=r,u}};function p(e,n){for(s=e,a=n,r=0;!d&&l&&!i&&r<c.length;r++){var i,o=c[r],p=h.p,f=o[2];e>3?(i=f===n)&&(a=o[(s=o[4])?5:(s=3,3)],o[4]=o[5]=t):o[0]<=p&&((i=e<2&&p<o[1])?(s=0,h.v=n,h.n=o[1]):p<f&&(i=e<3||o[0]>n||n>f)&&(o[4]=e,o[5]=n,h.n=f,s=0))}if(i||e>1)return u;throw d=!0,n}return function(i,c,f){if(l>1)throw TypeError("Generator is already running");for(d&&1===c&&p(c,f),s=c,a=f;(r=s<2?t:a)||!d;){o||(s?s<3?(s>1&&(h.n=-1),p(s,a)):h.n=a:h.v=a);try{if(l=2,o){if(s||(i="next"),r=o[i]){if(!(r=r.call(o,a)))throw TypeError("iterator result is not an object");if(!r.done)return r;a=r.value,s<2&&(s=0)}else 1===s&&(r=o.return)&&r.call(o),s<2&&(a=TypeError("The iterator does not provide a '"+i+"' method"),s=1);o=t}else if((r=(d=h.n<0)?a:e.call(n,h))!==u)break}catch(e){o=t,s=1,a=e}finally{l=1}}return{value:r,done:d}}}(e,o,s),!0),a}var u={};function c(){}function d(){}function h(){}r=Object.getPrototypeOf;var p=[][s]?r(r([][s]())):(n(r={},s,function(){return this}),r),f=h.prototype=c.prototype=Object.create(p);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,n(e,a,"GeneratorFunction")),e.prototype=Object.create(f),e}return d.prototype=h,n(f,"constructor",h),n(h,"constructor",d),d.displayName="GeneratorFunction",n(h,a,"GeneratorFunction"),n(f),n(f,a,"Generator"),n(f,s,function(){return this}),n(f,"toString",function(){return"[object Generator]"}),(e.exports=i=function(){return{w:l,m:m}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports},9829:function(e,t,r){var n=r(2962);e.exports=function(e,t,r,i,o){var s=n(e,t,r,i,o);return s.next().then(function(e){return e.done?e.value:s.next()})},e.exports.__esModule=!0,e.exports.default=e.exports},2962:function(e,t,r){var n=r(5015),i=r(7076);e.exports=function(e,t,r,o,s){return new i(n().w(e,t,r,o),s||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},7076:function(e,t,r){var n=r(6459),i=r(6688);e.exports=function e(t,r){var o;this.next||(i(e.prototype),i(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),i(this,"_invoke",function(e,i,s){function a(){return new r(function(i,o){(function e(i,o,s,a){try{var l=t[i](o),u=l.value;return u instanceof n?r.resolve(u.v).then(function(t){e("next",t,s,a)},function(t){e("throw",t,s,a)}):r.resolve(u).then(function(e){l.value=e,s(l)},function(t){return e("throw",t,s,a)})}catch(e){a(e)}})(e,s,i,o)})}return o=o?o.then(a,a):a()},!0)},e.exports.__esModule=!0,e.exports.default=e.exports},6688:function(e){function t(r,n,i,o){var s=Object.defineProperty;try{s({},"",{})}catch(e){s=0}e.exports=t=function(e,r,n,i){if(r)s?s(e,r,{value:n,enumerable:!i,configurable:!i,writable:!i}):e[r]=n;else{var o=function(r,n){t(e,r,function(e){return this._invoke(r,n,e)})};o("next",0),o("throw",1),o("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,i,o)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},1519:function(e){e.exports=function(e){var t=Object(e),r=[];for(var n in t)r.unshift(n);return function e(){for(;r.length;)if((n=r.pop())in t)return e.value=n,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},7609:function(e,t,r){var n=r(6459),i=r(5015),o=r(9829),s=r(2962),a=r(7076),l=r(1519),u=r(3357);function c(){"use strict";var t=i(),r=t.m(c),d=(Object.getPrototypeOf?Object.getPrototypeOf(r):r.__proto__).constructor;function h(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))}var p={throw:1,return:2,break:3,continue:3};function f(e){var t,r;return function(n){t||(t={stop:function(){return r(n.a,2)},catch:function(){return n.v},abrupt:function(e,t){return r(n.a,p[e],t)},delegateYield:function(e,i,o){return t.resultName=i,r(n.d,u(e),o)},finish:function(e){return r(n.f,e)}},r=function(e,r,i){n.p=t.prev,n.n=t.next;try{return e(r,i)}finally{t.next=n.n}}),t.resultName&&(t[t.resultName]=n.v,t.resultName=void 0),t.sent=n.v,t.next=n.n;try{return e.call(this,t)}finally{n.p=t.prev,n.n=t.next}}}return(e.exports=c=function(){return{wrap:function(e,r,n,i){return t.w(f(e),r,n,i&&i.reverse())},isGeneratorFunction:h,mark:t.m,awrap:function(e,t){return new n(e,t)},AsyncIterator:a,async:function(e,t,r,n,i){return(h(t)?s:o)(f(e),t,r,n,i)},keys:l,values:u}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports},3357:function(e,t,r){var n=r(7425).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}throw TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},8560:function(e){function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3681:function(e,t,r){var n=r(6314),i=r(6193),o=r(121),s=r(6147);e.exports=function(e,t){return n(e)||i(e,t)||o(e,t)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},8064:function(e,t,r){var n=r(7425).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},7739:function(e,t,r){var n=r(7425).default,i=r(8064);e.exports=function(e){var t=i(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},7425:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},121:function(e,t,r){var n=r(5748);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},8365:function(e,t,r){var n=r(1147),i=r(8560),o=r(9549),s=r(3419);function a(t){var r="function"==typeof Map?new Map:void 0;return e.exports=a=function(e){if(null===e||!o(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return s(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,a(t)}e.exports=a,e.exports.__esModule=!0,e.exports.default=e.exports},2841:function(e,t,r){var n=r(7609)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},6763:function(e,t,r){"use strict";var n=r(2784);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))});t.Z=i},5116:function(e,t,r){"use strict";var n=r(2784);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=i},5239:function(e,t,r){"use strict";r.d(t,{M:function(){return g}});var n=r(2784),i=r(3617);function o(){let e=(0,n.useRef)(!1);return(0,i.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var s=r(2972),a=r(7967),l=r(3105);class u extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t}){let r=(0,n.useId)(),i=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:e,height:n,top:s,left:a}=o.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${s}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),n.createElement(u,{isPresent:t,childRef:i,sizeRef:o},n.cloneElement(e,{ref:i}))}let d=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:o,presenceAffectsLayout:s,mode:u})=>{let d=(0,l.h)(h),p=(0,n.useId)(),f=(0,n.useMemo)(()=>({id:p,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;i&&i()},register:e=>(d.set(e,!1),()=>d.delete(e))}),s?void 0:[r]);return(0,n.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[r]),n.useEffect(()=>{r||d.size||!i||i()},[r]),"popLayout"===u&&(e=n.createElement(c,{isPresent:r},e)),n.createElement(a.O.Provider,{value:f},e)};function h(){return new Map}var p=r(3422),f=r(7035);let m=e=>e.key||"",g=({children:e,custom:t,initial:r=!0,onExitComplete:a,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:c="sync"})=>{var h;(0,f.k)(!l,"Replace exitBeforeEnter with mode='wait'");let g=(0,n.useContext)(p.p).forceRender||function(){let e=o(),[t,r]=(0,n.useState)(0),i=(0,n.useCallback)(()=>{e.current&&r(t+1)},[t]),a=(0,n.useCallback)(()=>s.Wi.postRender(i),[i]);return[a,t]}()[0],y=o(),v=function(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}(e),b=v,x=(0,n.useRef)(new Map).current,w=(0,n.useRef)(b),S=(0,n.useRef)(new Map).current,k=(0,n.useRef)(!0);if((0,i.L)(()=>{k.current=!1,function(e,t){e.forEach(e=>{let r=m(e);t.set(r,e)})}(v,S),w.current=b}),h=()=>{k.current=!0,S.clear(),x.clear()},(0,n.useEffect)(()=>()=>h(),[]),k.current)return n.createElement(n.Fragment,null,b.map(e=>n.createElement(d,{key:m(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:c},e)));b=[...b];let P=w.current.map(m),E=v.map(m),O=P.length;for(let e=0;e<O;e++){let t=P[e];-1!==E.indexOf(t)||x.has(t)||x.set(t,void 0)}return"wait"===c&&x.size&&(b=[]),x.forEach((e,r)=>{if(-1!==E.indexOf(r))return;let i=S.get(r);if(!i)return;let o=P.indexOf(r),s=e;s||(s=n.createElement(d,{key:m(i),isPresent:!1,onExitComplete:()=>{x.delete(r);let e=Array.from(S.keys()).filter(e=>!E.includes(e));if(e.forEach(e=>S.delete(e)),w.current=v.filter(t=>{let n=m(t);return n===r||e.includes(n)}),!x.size){if(!1===y.current)return;g(),a&&a()}},custom:t,presenceAffectsLayout:u,mode:c},i),x.set(r,s)),b.splice(o,0,s)}),b=b.map(e=>{let t=e.key;return x.has(t)?e:n.createElement(d,{key:m(e),isPresent:!0,presenceAffectsLayout:u,mode:c},e)}),n.createElement(n.Fragment,null,x.size?b:b.map(e=>(0,n.cloneElement)(e)))}},3422:function(e,t,r){"use strict";r.d(t,{p:function(){return i}});var n=r(2784);let i=(0,n.createContext)({})},7967:function(e,t,r){"use strict";r.d(t,{O:function(){return i}});var n=r(2784);let i=(0,n.createContext)(null)},2972:function(e,t,r){"use strict";r.d(t,{Pn:function(){return a},Wi:function(){return s},frameData:function(){return l},S6:function(){return u}});var n=r(65);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let o=["prepare","read","update","preRender","render","postRender"],{schedule:s,cancel:a,state:l,steps:u}=function(e,t){let r=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},a=o.reduce((e,t)=>(e[t]=function(e){let t=new i,r=new i,n=0,o=!1,s=!1,a=new WeakSet,l={schedule:(e,i=!1,s=!1)=>{let l=s&&o,u=l?t:r;return i&&a.add(e),u.add(e)&&l&&o&&(n=t.order.length),e},cancel:e=>{r.remove(e),a.delete(e)},process:i=>{if(o){s=!0;return}if(o=!0,[t,r]=[r,t],r.clear(),n=t.order.length)for(let r=0;r<n;r++){let n=t.order[r];n(i),a.has(n)&&(l.schedule(n),e())}o=!1,s&&(s=!1,l.process(i))}};return l}(()=>r=!0),e),{}),l=e=>a[e].process(s),u=()=>{let i=performance.now();r=!1,s.delta=n?1e3/60:Math.max(Math.min(i-s.timestamp,40),1),s.timestamp=i,s.isProcessing=!0,o.forEach(l),s.isProcessing=!1,r&&t&&(n=!1,e(u))},c=()=>{r=!0,n=!0,s.isProcessing||e(u)},d=o.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||c(),n.schedule(e,t,i)),e},{});return{schedule:d,cancel:e=>o.forEach(t=>a[t].cancel(e)),state:s,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},8508:function(e,t,r){"use strict";let n;r.d(t,{E:function(){return iZ}});var i,o,s=r(2784);let a=(0,s.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),l=(0,s.createContext)({});var u=r(7967),c=r(3617);let d=(0,s.createContext)({strict:!1}),h=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+h("framerAppearId");function f(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function m(e){return"string"==typeof e||Array.isArray(e)}function g(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],v=["initial",...y];function b(e){return g(e.animate)||v.some(t=>m(e[t]))}function x(e){return!!(b(e)||e.variants)}function w(e){return Array.isArray(e)?e.join(" "):e}let S={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},k={};for(let e in S)k[e]={isEnabled:t=>S[e].some(e=>!!t[e])};var P=r(3791),E=r(3422);let O=(0,s.createContext)({}),j=Symbol.for("motionComponentSymbol"),C=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function T(e){if("string"!=typeof e||e.includes("-"));else if(C.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let L={},A=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],R=new Set(A);function M(e,{layout:t,layoutId:r}){return R.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!L[e]||"opacity"===e)}let N=e=>!!(e&&e.getVelocity),_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},D=A.length,V=e=>t=>"string"==typeof t&&t.startsWith(e),$=V("--"),F=V("var(--"),I=(e,t)=>t&&"number"==typeof e?t.transform(e):e,B=(e,t,r)=>Math.min(Math.max(r,e),t),U={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},W={...U,transform:e=>B(0,1,e)},z={...U,default:1},H=e=>Math.round(1e5*e)/1e5,K=/(-)?([\d]*\.?[\d])+/g,Z=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,G=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(e){return"string"==typeof e}let Y=e=>({test:t=>q(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),J=Y("deg"),X=Y("%"),Q=Y("px"),ee=Y("vh"),et=Y("vw"),er={...X,parse:e=>X.parse(e)/100,transform:e=>X.transform(100*e)},en={...U,transform:Math.round},ei={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:J,rotateX:J,rotateY:J,rotateZ:J,scale:z,scaleX:z,scaleY:z,scaleZ:z,skew:J,skewX:J,skewY:J,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:W,originX:er,originY:er,originZ:Q,zIndex:en,fillOpacity:W,strokeOpacity:W,numOctaves:en};function eo(e,t,r,n){let{style:i,vars:o,transform:s,transformOrigin:a}=e,l=!1,u=!1,c=!0;for(let e in t){let r=t[e];if($(e)){o[e]=r;continue}let n=ei[e],d=I(r,n);if(R.has(e)){if(l=!0,s[e]=d,!c)continue;r!==(n.default||0)&&(c=!1)}else e.startsWith("origin")?(u=!0,a[e]=d):i[e]=d}if(!t.transform&&(l||n?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,i){let o="";for(let t=0;t<D;t++){let r=A[t];if(void 0!==e[r]){let t=_[r]||r;o+=`${t}(${e[r]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,n?"":o):r&&n&&(o="none"),o}(e.transform,r,c,n):i.transform&&(i.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=a;i.transformOrigin=`${e} ${t} ${r}`}}let es=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ea(e,t,r){for(let n in t)N(t[n])||M(n,r)||(e[n]=t[n])}function el(e,t,r){let n={},i=function(e,t,r){let n=e.style||{},i={};return ea(i,n,e),Object.assign(i,function({transformTemplate:e},t,r){return(0,s.useMemo)(()=>{let n=es();return eo(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(i):i}(e,t,r);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=i,n}let eu=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ec(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||eu.has(e)}let ed=e=>!ec(e);try{(i=require("@emotion/is-prop-valid").default)&&(ed=e=>e.startsWith("on")?!ec(e):i(e))}catch(e){}function eh(e,t,r){return"string"==typeof e?e:Q.transform(t+r*e)}let ep={offset:"stroke-dashoffset",array:"stroke-dasharray"},ef={offset:"strokeDashoffset",array:"strokeDasharray"};function em(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d,h){if(eo(e,u,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f,dimensions:m}=e;p.transform&&(m&&(f.transform=p.transform),delete p.transform),m&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,r){let n=eh(t,e.x,e.width),i=eh(r,e.y,e.height);return`${n} ${i}`}(m,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(p.x=t),void 0!==r&&(p.y=r),void 0!==n&&(p.scale=n),void 0!==s&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?ep:ef;e[o.offset]=Q.transform(-n);let s=Q.transform(t),a=Q.transform(r);e[o.array]=`${s} ${a}`}(p,s,a,l,!1)}let eg=()=>({...es(),attrs:{}}),ey=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ev(e,t,r,n){let i=(0,s.useMemo)(()=>{let r=eg();return em(r,t,{enableHardwareAcceleration:!1},ey(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};ea(t,e.style,e),i.style={...t,...i.style}}return i}function eb(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}let ex=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ew(e,t,r,n){for(let r in eb(e,t,void 0,n),t.attrs)e.setAttribute(ex.has(r)?r:h(r),t.attrs[r])}function eS(e,t){let{style:r}=e,n={};for(let i in r)(N(r[i])||t.style&&N(t.style[i])||M(i,e))&&(n[i]=r[i]);return n}function ek(e,t){let r=eS(e,t);for(let n in e)if(N(e[n])||N(t[n])){let t=-1!==A.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n;r[t]=e[n]}return r}function eP(e,t,r,n={},i={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),t}var eE=r(3105);let eO=e=>Array.isArray(e),ej=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),eC=e=>eO(e)?e[e.length-1]||0:e;function eT(e){let t=N(e)?e.get():e;return ej(t)?t.toValue():t}let eL=e=>(t,r)=>{let n=(0,s.useContext)(l),i=(0,s.useContext)(u.O),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,i,o){let s={latestValues:function(e,t,r,n){let i={},o=n(e,{});for(let e in o)i[e]=eT(o[e]);let{initial:s,animate:a}=e,l=b(e),u=x(e);t&&u&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===a&&(a=t.animate));let c=!!r&&!1===r.initial;c=c||!1===s;let d=c?a:s;if(d&&"boolean"!=typeof d&&!g(d)){let t=Array.isArray(d)?d:[d];t.forEach(t=>{let r=eP(e,t);if(!r)return;let{transitionEnd:n,transition:o,...s}=r;for(let e in s){let t=s[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let e in n)i[e]=n[e]})}return i}(n,i,o,e),renderState:t()};return r&&(s.mount=e=>r(n,e,s)),s})(e,t,n,i);return r?o():(0,eE.h)(o)};var eA=r(2972);let eR={useVisualState:eL({scrapeMotionValuesFromProps:ek,createRenderState:eg,onMount:(e,t,{renderState:r,latestValues:n})=>{eA.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),eA.Wi.render(()=>{em(r,n,{enableHardwareAcceleration:!1},ey(t.tagName),e.transformTemplate),ew(t,r)})}})},eM={useVisualState:eL({scrapeMotionValuesFromProps:eS,createRenderState:es})};function eN(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let e_=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eD(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eV=e=>t=>e_(t)&&e(t,eD(t));function e$(e,t,r,n){return eN(e,t,eV(r),n)}let eF=(e,t)=>r=>t(e(r)),eI=(...e)=>e.reduce(eF);function eB(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eU=eB("dragHorizontal"),eW=eB("dragVertical");function ez(e){let t=!1;if("y"===e)t=eW();else if("x"===e)t=eU();else{let e=eU(),r=eW();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function eH(){let e=ez(!0);return!e||(e(),!1)}class eK{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eZ(e,t){let r="onHover"+(t?"Start":"End");return e$(e.current,"pointer"+(t?"enter":"leave"),(n,i)=>{if("touch"===n.pointerType||eH())return;let o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&eA.Wi.update(()=>o[r](n,i))},{passive:!e.getProps()[r]})}class eG extends eK{mount(){this.unmount=eI(eZ(this.node,!0),eZ(this.node,!1))}unmount(){}}class eq extends eK{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eI(eN(this.node.current,"focus",()=>this.onFocus()),eN(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eY=(e,t)=>!!t&&(e===t||eY(e,t.parentElement));var eJ=r(65);function eX(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,eD(r))}class eQ extends eK{constructor(){super(...arguments),this.removeStartListeners=eJ.Z,this.removeEndListeners=eJ.Z,this.removeAccessibleListeners=eJ.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=e$(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:n,globalTapTarget:i}=this.node.getProps();eA.Wi.update(()=>{i||eY(this.node.current,e.target)?r&&r(e,t):n&&n(e,t)})},{passive:!(r.onTap||r.onPointerUp)}),i=e$(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=eI(n,i),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eN(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eN(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eX("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&eA.Wi.update(()=>r(e,t))})}),eX("down",(e,t)=>{this.startPress(e,t)}))}),t=eN(this.node.current,"blur",()=>{this.isPressing&&eX("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eI(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&eA.Wi.update(()=>r(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;let e=this.node.getProps();return e.whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eH()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&eA.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=e$(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=eN(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eI(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let e0=new WeakMap,e1=new WeakMap,e2=e=>{let t=e0.get(e.target);t&&t(e)},e5=e=>{e.forEach(e2)},e3={some:0,all:1};class e4 extends eK{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:e3[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;e1.has(r)||e1.set(r,{});let n=e1.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(e5,{root:e,...t})),n[i]}(t);return e0.set(e,r),n.observe(e),()=>{e0.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node,r=["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t));r&&this.startObserver()}unmount(){}}function e6(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function e8(e,t,r){let n=e.getProps();return eP(n,t,void 0!==r?r:n.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var e7=r(7035);let e9=e=>1e3*e,te=e=>e/1e3,tt={current:!1},tr=e=>Array.isArray(e)&&"number"==typeof e[0],tn=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,ti={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tn([0,.65,.55,1]),circOut:tn([.55,0,1,.45]),backIn:tn([.31,.01,.66,-.59]),backOut:tn([.33,1.53,.69,.99])},to=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function ts(e,t,r,n){if(e===t&&r===n)return eJ.Z;let i=t=>(function(e,t,r,n,i){let o,s;let a=0;do(o=to(s=t+(r-t)/2,n,i)-e)>0?r=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,r);return e=>0===e||1===e?e:to(i(e),t,n)}let ta=ts(.42,0,1,1),tl=ts(0,0,.58,1),tu=ts(.42,0,.58,1),tc=e=>Array.isArray(e)&&"number"!=typeof e[0],td=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,th=e=>t=>1-e(1-t),tp=e=>1-Math.sin(Math.acos(e)),tf=th(tp),tm=td(tp),tg=ts(.33,1.53,.69,.99),ty=th(tg),tv=td(ty),tb={linear:eJ.Z,easeIn:ta,easeInOut:tu,easeOut:tl,circIn:tp,circInOut:tm,circOut:tf,backIn:ty,backInOut:tv,backOut:tg,anticipate:e=>(e*=2)<1?.5*ty(e):.5*(2-Math.pow(2,-10*(e-1)))},tx=e=>{if(Array.isArray(e)){(0,e7.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return ts(t,r,n,i)}return"string"==typeof e?((0,e7.k)(void 0!==tb[e],`Invalid easing type '${e}'`),tb[e]):e},tw=(e,t)=>r=>!!(q(r)&&G.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),tS=(e,t,r)=>n=>{if(!q(n))return n;let[i,o,s,a]=n.match(K);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},tk=e=>B(0,255,e),tP={...U,transform:e=>Math.round(tk(e))},tE={test:tw("rgb","red"),parse:tS("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+tP.transform(e)+", "+tP.transform(t)+", "+tP.transform(r)+", "+H(W.transform(n))+")"},tO={test:tw("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:tE.transform},tj={test:tw("hsl","hue"),parse:tS("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+X.transform(H(t))+", "+X.transform(H(r))+", "+H(W.transform(n))+")"},tC={test:e=>tE.test(e)||tO.test(e)||tj.test(e),parse:e=>tE.test(e)?tE.parse(e):tj.test(e)?tj.parse(e):tO.parse(e),transform:e=>q(e)?e:e.hasOwnProperty("red")?tE.transform(e):tj.transform(e)},tT=(e,t,r)=>-r*e+r*t+e;function tL(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}let tA=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},tR=[tO,tE,tj],tM=e=>tR.find(t=>t.test(e));function tN(e){let t=tM(e);(0,e7.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===tj&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,s=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;i=tL(a,n,e+1/3),o=tL(a,n,e),s=tL(a,n,e-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:n}}(r)),r}let t_=(e,t)=>{let r=tN(e),n=tN(t),i={...r};return e=>(i.red=tA(r.red,n.red,e),i.green=tA(r.green,n.green,e),i.blue=tA(r.blue,n.blue,e),i.alpha=tT(r.alpha,n.alpha,e),tE.transform(i))},tD={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eJ.Z},tV={regex:Z,countKey:"Colors",token:"${c}",parse:tC.parse},t$={regex:K,countKey:"Numbers",token:"${n}",parse:U.parse};function tF(e,{regex:t,countKey:r,token:n,parse:i}){let o=e.tokenised.match(t);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...o.map(i)))}function tI(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&tF(r,tD),tF(r,tV),tF(r,t$),r}function tB(e){return tI(e).values}function tU(e){let{values:t,numColors:r,numVars:n,tokenised:i}=tI(e),o=t.length;return e=>{let t=i;for(let i=0;i<o;i++)t=i<n?t.replace(tD.token,e[i]):i<n+r?t.replace(tV.token,tC.transform(e[i])):t.replace(t$.token,H(e[i]));return t}}let tW=e=>"number"==typeof e?0:e,tz={test:function(e){var t,r;return isNaN(e)&&q(e)&&((null===(t=e.match(K))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(Z))||void 0===r?void 0:r.length)||0)>0},parse:tB,createTransformer:tU,getAnimatableNone:function(e){let t=tB(e),r=tU(e);return r(t.map(tW))}},tH=(e,t)=>r=>`${r>0?t:e}`;function tK(e,t){return"number"==typeof e?r=>tT(e,t,r):tC.test(e)?t_(e,t):e.startsWith("var(")?tH(e,t):tq(e,t)}let tZ=(e,t)=>{let r=[...e],n=r.length,i=e.map((e,r)=>tK(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}},tG=(e,t)=>{let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=tK(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}},tq=(e,t)=>{let r=tz.createTransformer(t),n=tI(e),i=tI(t),o=n.numVars===i.numVars&&n.numColors===i.numColors&&n.numNumbers>=i.numNumbers;return o?eI(tZ(n.values,i.values),r):((0,e7.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tH(e,t))},tY=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},tJ=(e,t)=>r=>tT(e,t,r);function tX(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if((0,e7.k)(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,r){let n=[],i=r||function(e){if("number"==typeof e);else if("string"==typeof e)return tC.test(e)?t_:tq;else if(Array.isArray(e))return tZ;else if("object"==typeof e)return tG;return tJ}(e[0]),o=e.length-1;for(let r=0;r<o;r++){let o=i(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||eJ.Z:t;o=eI(e,o)}n.push(o)}return n}(t,n,i),a=s.length,l=t=>{let r=0;if(a>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let n=tY(e[r],e[r+1],t);return s[r](n)};return r?t=>l(B(e[0],e[o-1],t)):l}function tQ({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=tc(n)?n.map(tx):tx(n),o={done:!1,value:t[0]},s=(r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=tY(0,t,n);e.push(tT(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),a=tX(s,t,{ease:Array.isArray(i)?i:t.map(()=>i||tu).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}function t0(e,t,r){var n,i;let o=Math.max(t-5,0);return n=r-e(o),(i=t-o)?n*(1e3/i):0}function t1(e,t){return e*Math.sqrt(1-t*t)}let t2=["duration","bounce"],t5=["stiffness","damping","mass"];function t3(e,t){return t.some(t=>void 0!==e[t])}function t4({keyframes:e,restDelta:t,restSpeed:r,...n}){let i;let o=e[0],s=e[e.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:d,velocity:h,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!t3(e,t5)&&t3(e,t2)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let i,o;(0,e7.K)(e<=e9(10),"Spring duration must be 10 seconds or less");let s=1-t;s=B(.05,1,s),e=B(.01,10,te(e)),s<1?(i=t=>{let n=t*s,i=n*e,o=t1(t,s);return .001-(n-r)/o*Math.exp(-i)},o=t=>{let n=t*s,o=n*e,a=Math.pow(s,2)*Math.pow(t,2)*e,l=t1(Math.pow(t,2),s),u=-i(t)+.001>0?-1:1;return u*((o*r+r-a)*Math.exp(-o))/l}):(i=t=>{let n=Math.exp(-t*e),i=(t-r)*e+1;return -.001+n*i},o=t=>{let n=Math.exp(-t*e),i=(r-t)*(e*e);return n*i});let a=5/e,l=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,a);if(e=e9(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(l,2)*n;return{stiffness:t,damping:2*s*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...n,velocity:-te(n.velocity||0)}),f=h||0,m=u/(2*Math.sqrt(l*c)),g=s-o,y=te(Math.sqrt(l/c)),v=5>Math.abs(g);if(r||(r=v?.01:2),t||(t=v?.005:.5),m<1){let e=t1(y,m);i=t=>{let r=Math.exp(-m*y*t);return s-r*((f+m*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(1===m)i=e=>s-Math.exp(-y*e)*(g+(f+y*g)*e);else{let e=y*Math.sqrt(m*m-1);i=t=>{let r=Math.exp(-m*y*t),n=Math.min(e*t,300);return s-r*((f+m*y*g)*Math.sinh(n)+e*g*Math.cosh(n))/e}}return{calculatedDuration:p&&d||null,next:e=>{let n=i(e);if(p)a.done=e>=d;else{let o=f;0!==e&&(o=m<1?t0(i,e,n):0);let l=Math.abs(o)<=r,u=Math.abs(s-n)<=t;a.done=l&&u}return a.value=a.done?s:n,a}}}function t6({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let p=e[0],f={done:!1,value:p},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,y=r*t,v=p+y,b=void 0===s?v:s(v);b!==v&&(y=b-p);let x=e=>-y*Math.exp(-e/n),w=e=>b+x(e),S=e=>{let t=x(e),r=w(e);f.done=Math.abs(t)<=u,f.value=f.done?b:r},k=e=>{m(f.value)&&(d=e,h=t4({keyframes:[f.value,g(f.value)],velocity:t0(w,e,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,S(e),k(e)),void 0!==d&&e>d)?h.next(e-d):(t||S(e),f)}}}let t8=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eA.Wi.update(t,!0),stop:()=>(0,eA.Pn)(t),now:()=>eA.frameData.isProcessing?eA.frameData.timestamp:performance.now()}};function t7(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let t9={decay:t6,inertia:t6,tween:tQ,keyframes:tQ,spring:t4};function re({autoplay:e=!0,delay:t=0,driver:r=t8,keyframes:n,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...h}){let p,f,m,g,y,v=1,b=!1,x=()=>{f=new Promise(e=>{p=e})};x();let w=t9[i]||tQ;w!==tQ&&"number"!=typeof n[0]&&(g=tX([0,100],n,{clamp:!1}),n=[0,100]);let S=w({...h,keyframes:n});"mirror"===a&&(y=w({...h,keyframes:[...n].reverse(),velocity:-(h.velocity||0)}));let k="idle",P=null,E=null,O=null;null===S.calculatedDuration&&o&&(S.calculatedDuration=t7(S));let{calculatedDuration:j}=S,C=1/0,T=1/0;null!==j&&(T=(C=j+s)*(o+1)-s);let L=0,A=e=>{if(null===E)return;v>0&&(E=Math.min(E,e)),v<0&&(E=Math.min(e-T/v,E)),L=null!==P?P:Math.round(e-E)*v;let r=L-t*(v>=0?1:-1),i=v>=0?r<0:r>T;L=Math.max(r,0),"finished"===k&&null===P&&(L=T);let l=L,u=S;if(o){let e=Math.min(L,T)/C,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,t=Math.min(t,o+1);let n=!!(t%2);n&&("reverse"===a?(r=1-r,s&&(r-=s/C)):"mirror"===a&&(u=y)),l=B(0,1,r)*C}let c=i?{done:!1,value:n[0]}:u.next(l);g&&(c.value=g(c.value));let{done:h}=c;i||null===j||(h=v>=0?L>=T:L<=0);let p=null===P&&("finished"===k||"running"===k&&h);return d&&d(c.value),p&&N(),c},R=()=>{m&&m.stop(),m=void 0},M=()=>{k="idle",R(),p(),x(),E=O=null},N=()=>{k="finished",c&&c(),R(),p()},_=()=>{if(b)return;m||(m=r(A));let e=m.now();l&&l(),null!==P?E=e-P:E&&"finished"!==k||(E=e),"finished"===k&&x(),O=E,P=null,k="running",m.start()};e&&_();let D={then:(e,t)=>f.then(e,t),get time(){return te(L)},set time(newTime){L=newTime=e9(newTime),null===P&&m&&0!==v?E=m.now()-newTime/v:P=newTime},get duration(){let e=null===S.calculatedDuration?t7(S):S.calculatedDuration;return te(e)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,D.time=te(L)},get state(){return k},play:_,pause:()=>{k="paused",P=L},stop:()=>{b=!0,"idle"!==k&&(k="idle",u&&u(),M())},cancel:()=>{null!==O&&A(O),M()},complete:()=>{k="finished"},sample:e=>(E=0,A(e))};return D}let rt=(o=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===n&&(n=o()),n)),rr=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),rn=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&ti[t]||tr(t)||Array.isArray(t)&&t.every(e))}(t.ease),ri={type:"spring",stiffness:500,damping:25,restSpeed:10},ro=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rs={type:"keyframes",duration:.8},ra={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rl=(e,{keyframes:t})=>t.length>2?rs:R.has(e)?e.startsWith("scale")?ro(t[1]):ri:ra,ru=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tz.test(t)||"0"===t)&&!t.startsWith("url(")),rc=new Set(["brightness","contrast","saturate","opacity"]);function rd(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(K)||[];if(!n)return e;let i=r.replace(n,""),o=rc.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}let rh=/([a-z-]*)\(.*?\)/g,rp={...tz,getAnimatableNone:e=>{let t=e.match(rh);return t?t.map(rd).join(" "):e}},rf={...ei,color:tC,backgroundColor:tC,outlineColor:tC,fill:tC,stroke:tC,borderColor:tC,borderTopColor:tC,borderRightColor:tC,borderBottomColor:tC,borderLeftColor:tC,filter:rp,WebkitFilter:rp},rm=e=>rf[e];function rg(e,t){let r=rm(e);return r!==rp&&(r=tz),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let ry=e=>/^0[^.\s]+$/.test(e);function rv(e,t){return e[t]||e.default||e}let rb={skipAnimations:!1},rx=(e,t,r,n={})=>i=>{let o=rv(n,e)||{},s=o.delay||n.delay||0,{elapsed:a=0}=n;a-=e9(s);let l=function(e,t,r,n){let i,o;let s=ru(t,r);i=Array.isArray(r)?[...r]:[null,r];let a=void 0!==n.from?n.from:e.get(),l=[];for(let e=0;e<i.length;e++){var u;null===i[e]&&(i[e]=0===e?a:i[e-1]),("number"==typeof(u=i[e])?0===u:null!==u?"none"===u||"0"===u||ry(u):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(o=i[e])}if(s&&l.length&&o)for(let e=0;e<l.length;e++){let r=l[e];i[r]=rg(t,o)}return i}(t,e,r,o),u=l[0],c=l[l.length-1],d=ru(e,u),h=ru(e,c);(0,e7.K)(d===h,`You are trying to animate ${e} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&(p={...p,...rl(e,p)}),p.duration&&(p.duration=e9(p.duration)),p.repeatDelay&&(p.repeatDelay=e9(p.repeatDelay)),!d||!h||tt.current||!1===o.type||rb.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let i=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:eJ.Z,pause:eJ.Z,stop:eJ.Z,then:e=>(e(),Promise.resolve()),cancel:eJ.Z,complete:eJ.Z});return t?re({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(tt.current?{...p,delay:0}:p);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:n,...i}){let o,s;let a=rt()&&rr.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type;if(!a)return!1;let l=!1,u=!1,c=()=>{s=new Promise(e=>{o=e})};c();let{keyframes:d,duration:h=300,ease:p,times:f}=i;if(rn(t,i)){let e=re({...i,repeat:0,delay:0}),t={done:!1,value:d[0]},r=[],n=0;for(;!t.done&&n<2e4;)t=e.sample(n),r.push(t.value),n+=10;f=void 0,d=r,h=n-10,p="linear"}let m=function(e,t,r,{delay:n=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t){if(t)return tr(t)?tn(t):Array.isArray(t)?t.map(e):ti[t]}(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(e.owner.current,t,d,{...i,duration:h,ease:p,times:f}),g=()=>{u=!1,m.cancel()},y=()=>{u=!0,eA.Wi.update(g),o(),c()};return m.onfinish=()=>{u||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let n=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[n]}(d,i)),n&&n(),y())},{then:(e,t)=>s.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,eJ.Z),get time(){return te(m.currentTime||0)},set time(newTime){m.currentTime=e9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return te(h)},play:()=>{l||(m.play(),(0,eA.Pn)(g))},pause:()=>m.pause(),stop:()=>{if(l=!0,"idle"===m.playState)return;let{currentTime:t}=m;if(t){let r=re({...i,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}y()},complete:()=>{u||m.finish()},cancel:y}}(t,e,p);if(r)return r}return re(p)};function rw(e){return!!(N(e)&&e.add)}let rS=e=>/^\-?\d*\.?\d+$/.test(e);function rk(e,t){-1===e.indexOf(t)&&e.push(t)}function rP(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class rE{constructor(){this.subscriptions=[]}add(e){return rk(this.subscriptions,e),()=>rP(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rO=e=>!isNaN(parseFloat(e)),rj={current:void 0};class rC{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:n}=eA.frameData;this.lastUpdated!==n&&(this.timeDelta=r,this.lastUpdated=n,eA.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eA.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rO(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new rE);let r=this.events[e].add(t);return"change"===e?()=>{r(),eA.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rj.current&&rj.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?e*(1e3/t):0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rT(e,t){return new rC(e,t)}let rL=e=>t=>t.test(e),rA=[U,Q,X,J,et,ee,{test:e=>"auto"===e,parse:e=>e}],rR=e=>rA.find(rL(e)),rM=[...rA,tC,tz],rN=e=>rM.find(rL(e));function r_(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(o=n);let u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let n=e.getValue(t),i=a[t];if(!n||void 0===i||c&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(c,t))continue;let s={delay:r,elapsed:0,...rv(o||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[p];if(r){let e=window.HandoffAppearAnimations(r,t,n,eA.Wi);null!==e&&(s.elapsed=e,s.isHandoff=!0)}}let d=!s.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(n,i);if("spring"===s.type&&(n.getVelocity()||s.velocity)&&(d=!1),n.animation&&(d=!1),d)continue;n.start(rx(t,n,i,e.shouldReduceMotion&&R.has(t)?{type:!1}:s));let h=n.animation;rw(l)&&(l.add(t),h.then(()=>l.remove(t))),u.push(h)}return s&&Promise.all(u).then(()=>{s&&function(e,t){let r=e8(e,t),{transitionEnd:n={},transition:i={},...o}=r?e.makeTargetAnimatable(r,!1):{};for(let t in o={...o,...n}){let r=eC(o[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,rT(r))}}(e,s)}),u}function rD(e,t,r={}){let n=e8(e,t,r.custom),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(r_(e,n,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,r=0,n=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>a-e*n;return Array.from(e.variantChildren).sort(rV).forEach((e,n)=>{e.notify("AnimationStart",t),s.push(rD(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,o+n,s,a,r)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([o(),s(r.delay)]);{let[e,t]="beforeChildren"===a?[o,s]:[s,o];return e().then(()=>t())}}function rV(e,t){return e.sortNodePosition(t)}let r$=[...y].reverse(),rF=y.length;function rI(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rB extends eK{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t)){let i=t.map(t=>rD(e,t,r));n=Promise.all(i)}else if("string"==typeof t)n=rD(e,t,r);else{let i="function"==typeof t?e8(e,t,r.custom):t;n=Promise.all(r_(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:rI(!0),whileInView:rI(),whileHover:rI(),whileTap:rI(),whileDrag:rI(),whileFocus:rI(),exit:rI()},n=!0,i=(t,r)=>{let n=e8(e,r);if(n){let{transition:e,transitionEnd:r,...i}=n;t={...t,...i,...r}}return t};function o(o,s){let a=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set,d={},h=1/0;for(let t=0;t<rF;t++){var p;let f=r$[t],y=r[f],v=void 0!==a[f]?a[f]:l[f],b=m(v),x=f===s?y.isActive:null;!1===x&&(h=t);let w=v===l[f]&&v!==a[f]&&b;if(w&&n&&e.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...d},!y.isActive&&null===x||!v&&!y.prevProp||g(v)||"boolean"==typeof v)continue;let S=(p=y.prevProp,"string"==typeof v?v!==p:!!Array.isArray(v)&&!e6(v,p)),k=S||f===s&&y.isActive&&!w&&b||t>h&&b,P=!1,E=Array.isArray(v)?v:[v],O=E.reduce(i,{});!1===x&&(O={});let{prevResolvedValues:j={}}=y,C={...j,...O},T=e=>{k=!0,c.has(e)&&(P=!0,c.delete(e)),y.needsAnimating[e]=!0};for(let e in C){let t=O[e],r=j[e];if(!d.hasOwnProperty(e))(eO(t)&&eO(r)?e6(t,r):t===r)?void 0!==t&&c.has(e)?T(e):y.protectedKeys[e]=!0:void 0!==t?T(e):c.add(e)}y.prevProp=v,y.prevResolvedValues=O,y.isActive&&(d={...d,...O}),n&&e.blockInitialAnimation&&(k=!1),k&&(!w||P)&&u.push(...E.map(e=>({animation:e,options:{type:f,...o}})))}if(c.size){let t={};c.forEach(r=>{let n=e.getBaseTarget(r);void 0!==n&&(t[r]=n)}),u.push({animation:t})}let f=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(f=!1),n=!1,f?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,n,i){var s;if(r[t].isActive===n)return Promise.resolve();null===(s=e.variantChildren)||void 0===s||s.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let a=o(i,t);for(let e in r)r[e].protectedKeys={};return a},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),g(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let rU=0;class rW extends eK{constructor(){super(...arguments),this.id=rU++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let rz=(e,t)=>Math.abs(e-t);class rH{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){let r=rz(e.x,t.x),n=rz(e.y,t.y);return Math.sqrt(r**2+n**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=eA.frameData;this.history.push({...n,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rK(t,this.transformPagePoint),eA.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rG("pointercancel"===e.type?this.lastMoveEventInfo:rK(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!e_(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=eD(e),s=rK(o,this.transformPagePoint),{point:a}=s,{timestamp:l}=eA.frameData;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rG(s,this.history)),this.removeListeners=eI(e$(this.contextWindow,"pointermove",this.handlePointerMove),e$(this.contextWindow,"pointerup",this.handlePointerUp),e$(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eA.Pn)(this.updatePoint)}}function rK(e,t){return t?{point:t(e.point)}:e}function rZ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rG({point:e},t){return{point:e,delta:rZ(e,rq(t)),offset:rZ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rq(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>e9(.1)));)r--;if(!n)return{x:0,y:0};let o=te(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,0)}}function rq(e){return e[e.length-1]}function rY(e){return e.max-e.min}function rJ(e,t=0,r=.01){return Math.abs(e-t)<=r}function rX(e,t,r,n=.5){e.origin=n,e.originPoint=tT(t.min,t.max,e.origin),e.scale=rY(r)/rY(t),(rJ(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tT(r.min,r.max,e.origin)-e.originPoint,(rJ(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rQ(e,t,r,n){rX(e.x,t.x,r.x,n?n.originX:void 0),rX(e.y,t.y,r.y,n?n.originY:void 0)}function r0(e,t,r){e.min=r.min+t.min,e.max=e.min+rY(t)}function r1(e,t,r){e.min=t.min-r.min,e.max=e.min+rY(t)}function r2(e,t,r){r1(e.x,t.x,r.x),r1(e.y,t.y,r.y)}function r5(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function r3(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function r4(e,t,r){return{min:r6(e,t),max:r6(e,r)}}function r6(e,t){return"number"==typeof e?e:e[t]||0}let r8=()=>({translate:0,scale:1,origin:0,originPoint:0}),r7=()=>({x:r8(),y:r8()}),r9=()=>({min:0,max:0}),ne=()=>({x:r9(),y:r9()});function nt(e){return[e("x"),e("y")]}function nr({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function nn(e){return void 0===e||1===e}function ni({scale:e,scaleX:t,scaleY:r}){return!nn(e)||!nn(t)||!nn(r)}function no(e){return ni(e)||ns(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ns(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function na(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function nl(e,t=0,r=1,n,i){e.min=na(e.min,t,r,n,i),e.max=na(e.max,t,r,n,i)}function nu(e,{x:t,y:r}){nl(e.x,t.translate,t.scale,t.originPoint),nl(e.y,r.translate,r.scale,r.originPoint)}function nc(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function nd(e,t){e.min=e.min+t,e.max=e.max+t}function nh(e,t,[r,n,i]){let o=void 0!==t[i]?t[i]:.5,s=tT(e.min,e.max,o);nl(e,t[r],t[n],s,t.scale)}let np=["x","scaleX","originX"],nf=["y","scaleY","originY"];function nm(e,t){nh(e.x,t,np),nh(e.y,t,nf)}function ng(e,t){return nr(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let ny=({current:e})=>e?e.ownerDocument.defaultView:null,nv=new WeakMap;class nb{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rH(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eD(e,"page").point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ez(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nt(e=>{let t=this.getAxisMotionValue(e).get()||0;if(X.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=rY(n);t=e*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&eA.Wi.update(()=>i(e,t),!1,!0);let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:s}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nt(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ny(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&eA.Wi.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!nx(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tT(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tT(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&f(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:r5(e.x,r,i),y:r5(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r4(e,"left","right"),y:r4(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nt(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!f(t))return!1;let n=t.current;(0,e7.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=ng(e,r),{scroll:i}=t;return i&&(nd(n.x,i.offset.x),nd(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),s={x:r3((e=i.layout.layoutBox).x,o.x),y:r3(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nr(e))}return s}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{},l=nt(s=>{if(!nx(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[s]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)});return Promise.all(l).then(s)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(rx(e,r,0,t))}stopAnimation(){nt(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nt(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),n=r[t];return n||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){nt(t=>{let{drag:r}=this.getProps();if(!nx(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-tT(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!f(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nt(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();n[e]=function(e,t){let r=.5,n=rY(e),i=rY(t);return i>n?r=tY(t.min,t.max-n,e.min):n>i&&(r=tY(e.min,e.max-i,t.min)),B(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),nt(t=>{if(!nx(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set(tT(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;nv.set(this.visualElement,this);let e=this.visualElement.current,t=e$(e,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),r=()=>{let{dragConstraints:e}=this.getProps();f(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",r);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),r();let o=eN(window,"resize",()=>this.scalePositionWithinConstraints()),s=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nt(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{o(),t(),i(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function nx(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class nw extends eK{constructor(e){super(e),this.removeGroupControls=eJ.Z,this.removeListeners=eJ.Z,this.controls=new nb(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eJ.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let nS=e=>(t,r)=>{e&&eA.Wi.update(()=>e(t,r))};class nk extends eK{constructor(){super(...arguments),this.removePointerDownListener=eJ.Z}onPointerDown(e){this.session=new rH(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ny(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:nS(e),onStart:nS(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&eA.Wi.update(()=>n(e,t))}}}mount(){this.removePointerDownListener=e$(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nP={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nE(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nO={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!Q.test(e))return e;e=parseFloat(e)}let r=nE(e,t.target.x),n=nE(e,t.target.y);return`${r}% ${n}%`}};class nj extends s.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign(L,nT),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nP.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||eA.Wi.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nC(e){let[t,r]=function(){let e=(0,s.useContext)(u.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:n}=e,i=(0,s.useId)();return(0,s.useEffect)(()=>n(i),[]),!t&&r?[!1,()=>r&&r(i)]:[!0]}(),n=(0,s.useContext)(E.p);return s.createElement(nj,{...e,layoutGroup:n,switchLayoutGroup:(0,s.useContext)(O),isPresent:t,safeToRemove:r})}let nT={borderRadius:{...nO,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nO,borderTopRightRadius:nO,borderBottomLeftRadius:nO,borderBottomRightRadius:nO,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=tz.parse(e);if(n.length>5)return e;let i=tz.createTransformer(e),o="number"!=typeof n[0]?1:0,s=r.x.scale*t.x,a=r.y.scale*t.y;n[0+o]/=s,n[1+o]/=a;let l=tT(s,a,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}},nL=["TopLeft","TopRight","BottomLeft","BottomRight"],nA=nL.length,nR=e=>"string"==typeof e?parseFloat(e):e,nM=e=>"number"==typeof e||Q.test(e);function nN(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let n_=nV(0,.5,tf),nD=nV(.5,.95,eJ.Z);function nV(e,t,r){return n=>n<e?0:n>t?1:r(tY(e,t,n))}function n$(e,t){e.min=t.min,e.max=t.max}function nF(e,t){n$(e.x,t.x),n$(e.y,t.y)}function nI(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nB(e,t,[r,n,i],o,s){!function(e,t=0,r=1,n=.5,i,o=e,s=e){if(X.test(t)){t=parseFloat(t);let e=tT(s.min,s.max,t/100);t=e-s.min}if("number"!=typeof t)return;let a=tT(o.min,o.max,n);e===o&&(a-=t),e.min=nI(e.min,t,r,a,i),e.max=nI(e.max,t,r,a,i)}(e,t[r],t[n],t[i],t.scale,o,s)}let nU=["x","scaleX","originX"],nW=["y","scaleY","originY"];function nz(e,t,r,n){nB(e.x,t,nU,r?r.x:void 0,n?n.x:void 0),nB(e.y,t,nW,r?r.y:void 0,n?n.y:void 0)}function nH(e){return 0===e.translate&&1===e.scale}function nK(e){return nH(e.x)&&nH(e.y)}function nZ(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function nG(e){return rY(e.x)/rY(e.y)}class nq{constructor(){this.members=[]}add(e){rk(this.members,e),e.scheduleRender()}remove(e){if(rP(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function nY(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(n=`translate3d(${i}px, ${o}px, 0) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:i}=r;e&&(n+=`rotate(${e}deg) `),t&&(n+=`rotateX(${t}deg) `),i&&(n+=`rotateY(${i}deg) `)}let s=e.x.scale*t.x,a=e.y.scale*t.y;return(1!==s||1!==a)&&(n+=`scale(${s}, ${a})`),n||"none"}let nJ=(e,t)=>e.depth-t.depth;class nX{constructor(){this.children=[],this.isDirty=!1}add(e){rk(this.children,e),this.isDirty=!0}remove(e){rP(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nJ),this.isDirty=!1,this.children.forEach(e)}}let nQ=["","X","Y","Z"],n0={visibility:"hidden"},n1=0,n2={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function n5({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=n1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,n2.totalNodes=n2.resolvedTargetDeltas=n2.recalculatedProjection=0,this.nodes.forEach(n6),this.nodes.forEach(ii),this.nodes.forEach(io),this.nodes.forEach(n8),window.MotionDebug&&window.MotionDebug.record(n2)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nX)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rE),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),n=({timestamp:i})=>{let o=i-r;o>=t&&((0,eA.Pn)(n),e(o-t))};return eA.Wi.read(n,!0),()=>(0,eA.Pn)(n)}(n,250),nP.hasAnimatedSinceResize&&(nP.hasAnimatedSinceResize=!1,this.nodes.forEach(ir))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||id,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!nZ(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...rv(i,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||ir(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eA.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(is),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;let e=this.isUpdateBlocked();if(e){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n9);return}this.isUpdating||this.nodes.forEach(ie),this.isUpdating=!1,this.nodes.forEach(it),this.nodes.forEach(n3),this.nodes.forEach(n4),this.clearAllSnapshots();let t=performance.now();eA.frameData.delta=B(0,1e3/60,t-eA.frameData.timestamp),eA.frameData.timestamp=t,eA.frameData.isProcessing=!0,eA.S6.update.process(eA.frameData),eA.S6.preRender.process(eA.frameData),eA.S6.render.process(eA.frameData),eA.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(n7),this.sharedNodes.forEach(ia)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eA.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eA.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++){let t=this.path[e];t.updateScroll()}let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!nK(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&(t||no(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),im((t=n).x),im(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ne();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(nd(t.x,r.offset.x),nd(t.y,r.offset.y)),t}removeElementScroll(e){let t=ne();nF(t,e);for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;if(n!==this.root&&i&&o.layoutScroll){if(i.isRoot){nF(t,e);let{scroll:r}=this.root;r&&(nd(t.x,-r.offset.x),nd(t.y,-r.offset.y))}nd(t.x,i.offset.x),nd(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){let r=ne();nF(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&nm(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),no(n.latestValues)&&nm(r,n.latestValues)}return no(this.latestValues)&&nm(r,this.latestValues),r}removeTransform(e){let t=ne();nF(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!no(r.latestValues))continue;ni(r.latestValues)&&r.updateSnapshot();let n=ne(),i=r.measurePageBox();nF(n,i),nz(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return no(this.latestValues)&&nz(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eA.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o,a=!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget);if(a)return;let{layout:l,layoutId:u}=this.options;if(this.layout&&(l||u)){if(this.resolvedRelativeTargetAt=eA.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),r2(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,r0(r.x,n.x,i.x),r0(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nF(this.target,this.layout.layoutBox),nu(this.target,this.targetDelta)):nF(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),r2(this.relativeTargetOrigin,this.target,e.target),nF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}n2.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ni(this.parent.latestValues)||ns(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eA.frameData.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nF(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,r,n=!1){let i,o;let s=r.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=r[a]).projectionDelta;let s=i.instance;(!s||!s.style||"contents"!==s.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nm(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nu(e,o)),n&&no(i.latestValues)&&nm(e,i.latestValues))}t.x=nc(t.x),t.y=nc(t.y)}}(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=r7(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r7(),this.projectionDeltaWithTransform=r7());let u=this.projectionTransform;rQ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=nY(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==s||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),n2.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},s=r7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=ne(),l=n?n.source:void 0,u=this.layout?this.layout.source:void 0,c=l!==u,d=this.getStack(),h=!d||d.members.length<=1,p=!!(c&&!h&&!0===this.options.crossfade&&!this.path.some(ic));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(il(s.x,e.x,n),il(s.y,e.y,n),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var l,u,d,f;r2(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,f=this.relativeTargetOrigin,iu(d.x,f.x,a.x,n),iu(d.y,f.y,a.y,n),r&&(l=this.relativeTarget,u=r,l.x.min===u.x.min&&l.x.max===u.x.max&&l.y.min===u.y.min&&l.y.max===u.y.max)&&(this.isProjectionDirty=!1),r||(r=ne()),nF(r,this.relativeTarget)}c&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=tT(0,void 0!==r.opacity?r.opacity:1,n_(n)),e.opacityExit=tT(void 0!==t.opacity?t.opacity:1,0,nD(n))):o&&(e.opacity=tT(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nA;i++){let o=`border${nL[i]}Radius`,s=nN(t,o),a=nN(r,o);if(void 0===s&&void 0===a)continue;s||(s=0),a||(a=0);let l=0===s||0===a||nM(s)===nM(a);l?(e[o]=Math.max(tT(nR(s),nR(a),n),0),(X.test(a)||X.test(s))&&(e[o]+="%")):e[o]=a}(t.rotate||r.rotate)&&(e.rotate=tT(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eA.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eA.Wi.update(()=>{nP.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=N(e)?e:rT(e);return n.start(rx("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ig(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||ne();let t=rY(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rY(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nF(t,r),nm(t,i),rQ(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nq);let r=this.sharedNodes.get(e);r.add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let n={};for(let t=0;t<nQ.length;t++){let i="rotate"+nQ[t];r[i]&&(n[i]=r[i],e.setStaticValue(i,0))}for(let t in e.render(),n)e.setStaticValue(t,n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return n0;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=eT(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eT(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!no(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=nY(this.projectionDeltaWithTransform,this.treeScale,s),i&&(n.transform=i(s,n.transform));let{x:a,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(t=s.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:n.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,L){if(void 0===s[e])continue;let{correct:t,applyTo:r}=L[e],i="none"===n.transform?s[e]:t(s[e],o);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=o===this?eT(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n9),this.root.sharedNodes.clear()}}}function n3(e){e.updateLayout()}function n4(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,o=r.source!==e.layout.source;"size"===i?nt(e=>{let n=o?r.measuredBox[e]:r.layoutBox[e],i=rY(n);n.min=t[e].min,n.max=n.min+i}):ig(i,r.layoutBox,t)&&nt(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],s=rY(t[n]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+s)});let s=r7();rQ(s,t,r.layoutBox);let a=r7();o?rQ(a,e.applyTransform(n,!0),r.measuredBox):rQ(a,t,r.layoutBox);let l=!nK(s),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let s=ne();r2(s,r.layoutBox,i.layoutBox);let a=ne();r2(a,t,o.layoutBox),nZ(s,a)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n6(e){n2.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n8(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n7(e){e.clearSnapshot()}function n9(e){e.clearMeasurements()}function ie(e){e.isLayoutDirty=!1}function it(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function ir(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ii(e){e.resolveTargetDelta()}function io(e){e.calcProjection()}function is(e){e.resetRotation()}function ia(e){e.removeLeadSnapshot()}function il(e,t,r){e.translate=tT(t.translate,0,r),e.scale=tT(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function iu(e,t,r,n){e.min=tT(t.min,r.min,n),e.max=tT(t.max,r.max,n)}function ic(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let id={duration:.45,ease:[.4,0,.1,1]},ih=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),ip=ih("applewebkit/")&&!ih("chrome/")?Math.round:eJ.Z;function im(e){e.min=ip(e.min),e.max=ip(e.max)}function ig(e,t,r){return"position"===e||"preserve-aspect"===e&&!rJ(nG(t),nG(r),.2)}let iy=n5({attachResizeListener:(e,t)=>eN(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iv={current:void 0},ib=n5({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!iv.current){let e=new iy({});e.mount(window),e.setOptions({layoutScroll:!0}),iv.current=e}return iv.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),ix=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function iw(e,t,r=1){(0,e7.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,i]=function(e){let t=ix.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}(e);if(!n)return;let o=window.getComputedStyle(t).getPropertyValue(n);if(o){let e=o.trim();return rS(e)?parseFloat(e):e}return F(i)?iw(i,t,r+1):i}let iS=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ik=e=>iS.has(e),iP=e=>Object.keys(e).some(ik),iE=e=>e===U||e===Q,iO=(e,t)=>parseFloat(e.split(", ")[t]),ij=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/);if(i)return iO(i[1],t);{let t=n.match(/^matrix\((.+)\)$/);return t?iO(t[1],e):0}},iC=new Set(["x","y","z"]),iT=A.filter(e=>!iC.has(e)),iL={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ij(4,13),y:ij(5,14)};iL.translateX=iL.x,iL.translateY=iL.y;let iA=(e,t,r)=>{let n=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};"none"===s&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{a[e]=iL[e](n,o)}),t.render();let l=t.measureViewportBox();return r.forEach(r=>{let n=t.getValue(r);n&&n.jump(a[r]),e[r]=iL[r](l,o)}),e},iR=(e,t,r={},n={})=>{t={...t},n={...n};let i=Object.keys(t).filter(ik),o=[],s=!1,a=[];if(i.forEach(i=>{let l;let u=e.getValue(i);if(!e.hasValue(i))return;let c=r[i],d=rR(c),h=t[i];if(eO(h)){let e=h.length,t=null===h[0]?1:0;d=rR(c=h[t]);for(let r=t;r<e&&null!==h[r];r++)l?(0,e7.k)(rR(h[r])===l,"All keyframes must be of the same type"):(l=rR(h[r]),(0,e7.k)(l===d||iE(d)&&iE(l),"Keyframes must be of the same dimension as the current value"))}else l=rR(h);if(d!==l){if(iE(d)&&iE(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof h?t[i]=parseFloat(h):Array.isArray(h)&&l===Q&&(t[i]=h.map(parseFloat))}else(null==d?void 0:d.transform)&&(null==l?void 0:l.transform)&&(0===c||0===h)?0===c?u.set(l.transform(c)):t[i]=d.transform(h):(s||(o=function(e){let t=[];return iT.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),s=!0),a.push(i),n[i]=void 0!==n[i]?n[i]:t[i],u.jump(h))}}),!a.length)return{target:t,transitionEnd:n};{let r=a.indexOf("height")>=0?window.pageYOffset:null,i=iA(t,e,a);return o.length&&o.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),P.j&&null!==r&&window.scrollTo({top:r}),{target:i,transitionEnd:n}}},iM=(e,t,r,n)=>{var i,o;let s=function(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};for(let i in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!F(t))return;let r=iw(t,n);r&&e.set(r)}),t){let e=t[i];if(!F(e))continue;let o=iw(e,n);o&&(t[i]=o,r||(r={}),void 0===r[i]&&(r[i]=e))}return{target:t,transitionEnd:r}}(e,t,n);return t=s.target,n=s.transitionEnd,i=t,o=n,iP(i)?iR(e,i,r,o):{target:i,transitionEnd:o}},iN={current:null},i_={current:!1},iD=new WeakMap,iV=Object.keys(k),i$=iV.length,iF=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],iI=v.length;class iB{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eA.Wi.render(this.render,!1,!0);let{latestValues:s,renderState:a}=i;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=a,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.isControllingVariants=b(t),this.isVariantNode=x(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==s[e]&&N(t)&&(t.set(s[e],!1),rw(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,iD.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),i_.current||function(){if(i_.current=!0,P.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>iN.current=e.matches;e.addListener(t),t()}else iN.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iN.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in iD.delete(this.current),this.projection&&this.projection.unmount(),(0,eA.Pn)(this.notifyUpdate),(0,eA.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=R.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eA.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,n,i){let o,s;for(let e=0;e<i$;e++){let r=iV[e],{isEnabled:n,Feature:i,ProjectionNode:a,MeasureLayout:l}=k[r];a&&(o=a),n(t)&&(!this.features[r]&&i&&(this.features[r]=new i(this)),l&&(s=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:n,dragConstraints:s,layoutScroll:a,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:!!n||s&&f(s),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:i,layoutScroll:a,layoutRoot:l})}return s}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<iF.length;t++){let r=iF[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){let{willChange:n}=t;for(let i in t){let o=t[i],s=r[i];if(N(o))e.addValue(i,o),rw(n)&&n.add(i);else if(N(s))e.addValue(i,rT(o,{owner:e})),rw(n)&&n.remove(i);else if(s!==o){if(e.hasValue(i)){let t=e.getValue(i);t.hasAnimated||t.set(o)}else{let t=e.getStaticValue(i);e.addValue(i,rT(void 0!==t?t:o,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<iI;e++){let r=v[e],n=this.props[r];(m(n)||!1===n)&&(t[r]=n)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=rT(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n="string"==typeof r||"object"==typeof r?null===(t=eP(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||N(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new rE),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class iU extends iB{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},i){let o=function(e,t,r){let n={};for(let i in e){let e=function(e,t){if(!t)return;let r=t[e]||t.default||t;return r.from}(i,t);if(void 0!==e)n[i]=e;else{let e=r.getValue(i);e&&(n[i]=e.get())}}return n}(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),o&&(o=n(o))),i){!function(e,t,r){var n,i;let o=Object.keys(t).filter(t=>!e.hasValue(t)),s=o.length;if(s)for(let a=0;a<s;a++){let s=o[a],l=t[s],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(n=r[s])&&void 0!==n?n:e.readValue(s))&&void 0!==i?i:t[s]),null!=u&&("string"==typeof u&&(rS(u)||ry(u))?u=parseFloat(u):!rN(u)&&tz.test(l)&&(u=rg(s,l)),e.addValue(s,rT(u,{owner:e})),void 0===r[s]&&(r[s]=u),null!==u&&e.setBaseTarget(s,u))}}(this,r,o);let e=iM(this,r,o,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class iW extends iU{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(R.has(t)){let e=rm(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=($(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return ng(e,t)}build(e,t,r,n){eo(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return eS(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;N(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,n){eb(e,t,r,n)}}class iz extends iU{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(R.has(t)){let e=rm(t);return e&&e.default||0}return t=ex.has(t)?t:h(t),e.getAttribute(t)}measureInstanceViewportBox(){return ne()}scrapeMotionValuesFromProps(e,t){return ek(e,t)}build(e,t,r,n){em(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){ew(e,t,r,n)}mount(e){this.isSVGTag=ey(e.tagName),super.mount(e)}}let iH=(e,t)=>T(e)?new iz(t,{enableHardwareAcceleration:!1}):new iW(t,{enableHardwareAcceleration:!0}),iK={animation:{Feature:rB},exit:{Feature:rW},inView:{Feature:e4},tap:{Feature:eQ},focus:{Feature:eq},hover:{Feature:eG},pan:{Feature:nk},drag:{Feature:nw,ProjectionNode:ib,MeasureLayout:nC},layout:{ProjectionNode:ib,MeasureLayout:nC}},iZ=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){e&&function(e){for(let t in e)k[t]={...k[t],...e[t]}}(e);let o=(0,s.forwardRef)(function(o,h){var g;let y;let v={...(0,s.useContext)(a),...o,layoutId:function({layoutId:e}){let t=(0,s.useContext)(E.p).id;return t&&void 0!==e?t+"-"+e:e}(o)},{isStatic:x}=v,S=function(e){let{initial:t,animate:r}=function(e,t){if(b(e)){let{initial:t,animate:r}=e;return{initial:!1===t||m(t)?t:void 0,animate:m(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,s.useContext)(l));return(0,s.useMemo)(()=>({initial:t,animate:r}),[w(t),w(r)])}(o),k=n(o,x);if(!x&&P.j){S.visualElement=function(e,t,r,n){let{visualElement:i}=(0,s.useContext)(l),o=(0,s.useContext)(d),h=(0,s.useContext)(u.O),f=(0,s.useContext)(a).reducedMotion,m=(0,s.useRef)();n=n||o.renderer,!m.current&&n&&(m.current=n(e,{visualState:t,parent:i,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:f}));let g=m.current;(0,s.useInsertionEffect)(()=>{g&&g.update(r,h)});let y=(0,s.useRef)(!!(r[p]&&!window.HandoffComplete));return(0,c.L)(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),(0,s.useEffect)(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(i,k,v,t);let r=(0,s.useContext)(O),n=(0,s.useContext)(d).strict;S.visualElement&&(y=S.visualElement.loadFeatures(v,n,e,r))}return s.createElement(l.Provider,{value:S},y&&S.visualElement?s.createElement(y,{visualElement:S.visualElement,...v}):null,r(i,o,(g=S.visualElement,(0,s.useCallback)(e=>{e&&k.mount&&k.mount(e),g&&(e?g.mount(e):g.unmount()),h&&("function"==typeof h?h(e):f(h)&&(h.current=e))},[g])),k,x,S.visualElement))});return o[j]=i,o}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,n)=>(r.has(n)||r.set(n,t(n)),r.get(n))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,n){let i=T(e)?eR:eM;return{...i,preloadedFeatures:r,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=T(t)?ev:el,l=a(r,i,o,t),u=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ed(i)||!0===r&&ec(i)||!t&&!ec(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),c={...u,...l,ref:n},{children:d}=r,h=(0,s.useMemo)(()=>N(d)?d.get():d,[d]);return(0,s.createElement)(t,{...c,children:h})}}(t),createVisualElement:n,Component:e}})(e,t,iK,iH))},7035:function(e,t,r){"use strict";r.d(t,{K:function(){return i},k:function(){return o}});var n=r(65);let i=n.Z,o=n.Z},3791:function(e,t,r){"use strict";r.d(t,{j:function(){return n}});let n="undefined"!=typeof document},65:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=e=>e},3105:function(e,t,r){"use strict";r.d(t,{h:function(){return i}});var n=r(2784);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},3617:function(e,t,r){"use strict";r.d(t,{L:function(){return o}});var n=r(2784),i=r(3791);let o=i.j?n.useLayoutEffect:n.useEffect},2202:function(e,t,r){"use strict";let n,i;r.d(t,{x7:function(){return ed},ZP:function(){return eh}});var o,s=r(2784);let a={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||a,u=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,h=(e,t)=>{let r="",n="",i="";for(let o in e){let s=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+s+";":n+="f"==o[1]?h(s,o):o+"{"+h(s,"k"==o[1]?"":t)+"}":"object"==typeof s?n+=h(s,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=s&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=h.p?h.p(o,s):o+":"+s+";")}return r+(t&&i?t+"{"+i+"}":i)+n},p={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},m=(e,t,r,n,i)=>{var o;let s=f(e),a=p[s]||(p[s]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(s));if(!p[a]){let t=s!==e?e:(e=>{let t,r,n=[{}];for(;t=u.exec(e.replace(c,""));)t[4]?n.shift():t[3]?(r=t[3].replace(d," ").trim(),n.unshift(n[0][r]=n[0][r]||{})):n[0][t[1]]=t[2].replace(d," ").trim();return n[0]})(e);p[a]=h(i?{["@keyframes "+a]:t}:t,r?"":"."+a)}let l=r&&p.g?p.g:null;return r&&(p.g=p[a]),o=p[a],l?t.data=t.data.replace(l,o):-1===t.data.indexOf(o)&&(t.data=n?o+t.data:t.data+o),a},g=(e,t,r)=>e.reduce((e,n,i)=>{let o=t[i];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":h(e,""):!1===e?"":e}return e+n+(null==o?"":o)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return m(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let v,b,x,w=y.bind({k:1});function S(e,t){let r=this||{};return function(){let n=arguments;function i(o,s){let a=Object.assign({},o),l=a.className||i.className;r.p=Object.assign({theme:b&&b()},a),r.o=/ *go\d+/.test(l),a.className=y.apply(r,n)+(l?" "+l:""),t&&(a.ref=s);let u=e;return e[0]&&(u=a.as||e,delete a.as),x&&u[0]&&x(a),v(u,a)}return t?t(i):i}}var k=e=>"function"==typeof e,P=(e,t)=>k(e)?e(t):e,E=(n=0,()=>(++n).toString()),O=()=>{if(void 0===i&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");i=!e||e.matches}return i},j=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return j(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},C=[],T={toasts:[],pausedAt:void 0},L=e=>{T=j(T,e),C.forEach(e=>{e(T)})},A={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},R=(e={})=>{let[t,r]=(0,s.useState)(T),n=(0,s.useRef)(T);(0,s.useEffect)(()=>(n.current!==T&&r(T),C.push(r),()=>{let e=C.indexOf(r);e>-1&&C.splice(e,1)}),[]);let i=t.toasts.map(t=>{var r,n,i;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(n=e[t.type])?void 0:n.duration)||(null==e?void 0:e.duration)||A[t.type],style:{...e.style,...null==(i=e[t.type])?void 0:i.style,...t.style}}});return{...t,toasts:i}},M=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),N=e=>(t,r)=>{let n=M(t,e,r);return L({type:2,toast:n}),n.id},_=(e,t)=>N("blank")(e,t);_.error=N("error"),_.success=N("success"),_.loading=N("loading"),_.custom=N("custom"),_.dismiss=e=>{L({type:3,toastId:e})},_.remove=e=>L({type:4,toastId:e}),_.promise=(e,t,r)=>{let n=_.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let i=t.success?P(t.success,e):void 0;return i?_.success(i,{id:n,...r,...null==r?void 0:r.success}):_.dismiss(n),e}).catch(e=>{let i=t.error?P(t.error,e):void 0;i?_.error(i,{id:n,...r,...null==r?void 0:r.error}):_.dismiss(n)}),e};var D=(e,t)=>{L({type:1,toast:{id:e,height:t}})},V=()=>{L({type:5,time:Date.now()})},$=new Map,F=1e3,I=(e,t=F)=>{if($.has(e))return;let r=setTimeout(()=>{$.delete(e),L({type:4,toastId:e})},t);$.set(e,r)},B=e=>{let{toasts:t,pausedAt:r}=R(e);(0,s.useEffect)(()=>{if(r)return;let e=Date.now(),n=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&_.dismiss(t.id);return}return setTimeout(()=>_.dismiss(t.id),r)});return()=>{n.forEach(e=>e&&clearTimeout(e))}},[t,r]);let n=(0,s.useCallback)(()=>{r&&L({type:6,time:Date.now()})},[r]),i=(0,s.useCallback)((e,r)=>{let{reverseOrder:n=!1,gutter:i=8,defaultPosition:o}=r||{},s=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),a=s.findIndex(t=>t.id===e.id),l=s.filter((e,t)=>t<a&&e.visible).length;return s.filter(e=>e.visible).slice(...n?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+i,0)},[t]);return(0,s.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)I(e.id,e.removeDelay);else{let t=$.get(e.id);t&&(clearTimeout(t),$.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:D,startPause:V,endPause:n,calculateOffset:i}}},U=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,W=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,z=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,H=S("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${W} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${z} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,K=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Z=S("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${K} 1s linear infinite;
`,G=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,q=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Y=S("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${q} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=S("div")`
  position: absolute;
`,X=S("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Q=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=S("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Q} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:n}=e;return void 0!==t?"string"==typeof t?s.createElement(ee,null,t):t:"blank"===r?null:s.createElement(X,null,s.createElement(Z,{...n}),"loading"!==r&&s.createElement(J,null,"error"===r?s.createElement(H,{...n}):s.createElement(Y,{...n})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,en=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ei=S("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eo=S("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,es=(e,t)=>{let r=e.includes("top")?1:-1,[n,i]=O()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),en(r)];return{animation:t?`${w(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},ea=s.memo(({toast:e,position:t,style:r,children:n})=>{let i=e.height?es(e.position||t||"top-center",e.visible):{opacity:0},o=s.createElement(et,{toast:e}),a=s.createElement(eo,{...e.ariaProps},P(e.message,e));return s.createElement(ei,{className:e.className,style:{...i,...r,...e.style}},"function"==typeof n?n({icon:o,message:a}):s.createElement(s.Fragment,null,o,a))});o=s.createElement,h.p=void 0,v=o,b=void 0,x=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:n,children:i})=>{let o=s.useCallback(t=>{if(t){let r=()=>{n(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,n]);return s.createElement("div",{ref:o,className:t,style:r},i)},eu=(e,t)=>{let r=e.includes("top"),n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:O()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...n}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ed=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:n,children:i,containerStyle:o,containerClassName:a})=>{let{toasts:l,handlers:u}=B(r);return s.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:a,onMouseEnter:u.startPause,onMouseLeave:u.endPause},l.map(r=>{let o=r.position||t,a=eu(o,u.calculateOffset(r,{reverseOrder:e,gutter:n,defaultPosition:t}));return s.createElement(el,{id:r.id,key:r.id,onHeightUpdate:u.updateHeight,className:r.visible?ec:"",style:a},"custom"===r.type?P(r.message,r):i?i(r):s.createElement(ea,{toast:r,position:o}))}))},eh=_}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[774,179],function(){return t(6570),t(5123)}),_N_E=e.O()}]);