"use strict";exports.id=931,exports.ids=[931],exports.modules={6809:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>p});var s=t(997),o=t(6689),i=t(6197),l=t(5632),n=t(1675),c=t(3496),d=t(5342),m=t(6131),u=t(5126),g=e([i]);i=(g.then?(await g)():g)[0];let h=(0,o.forwardRef)(({label:e,placeholder:a,type:t="text",value:r,onChange:g,onBlur:h,error:p,touched:x=!1,required:b=!1,disabled:E=!1,autoComplete:f,leftIcon:N,rightIcon:v,onRightIconClick:w,className:y="",showPasswordStrength:S=!1,passwordStrength:j},I)=>{var T,A;let[_,R]=(0,o.useState)(!1),[C,D]=(0,o.useState)(!1),{currentTheme:O,themeName:P}=(0,n.Fg)(),F=(0,l.useRouter)(),{locale:L}=F,k="ar"===L,M="gold"===P,z=x&&p,$=r.length>0,U="password"===t,V=U&&_?"text":t,Z=`
    relative w-full mb-6 group
    ${y}
  `,B=`
    absolute transition-all duration-300 pointer-events-none
    ${k?"right-4 font-tajawal":"left-4 font-sans"}
    ${$||C?`top-2 text-xs ${k?"right-3":"left-3"} px-2 rounded-md`:"top-4 text-base"}
    ${z?"text-red-400":C?O.colors.text.accent:O.colors.text.secondary}
  `,G=`
    w-full h-14 px-4 pt-6 pb-2 rounded-xl transition-all duration-300
    ${k?"text-right font-tajawal pr-4":"text-left font-sans pl-4"}
    ${N?k?"pr-12":"pl-12":""}
    ${v||U?k?"pl-12":"pr-12":""}
    text-white placeholder-transparent
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
    disabled:opacity-50 disabled:cursor-not-allowed
    ${z?"border-2 border-red-400 focus:ring-red-400":`border border-white/20 focus:ring-2 ${M?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50"}`}
  `,Y=$||C?{background:M?"linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1))":"linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(126, 34, 206, 0.1))",backdropFilter:"blur(10px)",WebkitBackdropFilter:"blur(10px)"}:{};return(0,s.jsxs)(i.motion.div,{className:Z,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsxs)("div",{className:"relative",children:[N&&s.jsx("div",{className:`absolute top-1/2 transform -translate-y-1/2 ${k?"right-4":"left-4"} text-white/60 z-10`,children:N}),s.jsx("input",{ref:I,type:V,value:r,onChange:e=>g(e.target.value),onFocus:()=>D(!0),onBlur:()=>{D(!1),h&&h()},placeholder:a,required:b,disabled:E,autoComplete:f,className:G,style:{background:z?"rgba(239, 68, 68, 0.1)":"rgba(255, 255, 255, 0.08)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},dir:k?"rtl":"ltr"}),(0,s.jsxs)("label",{className:B,style:Y,children:[e,b&&s.jsx("span",{className:"text-red-400 ml-1",children:"*"})]}),s.jsx("div",{className:`absolute top-1/2 transform -translate-y-1/2 ${k?"left-4":"right-4"} z-10`,children:U?s.jsx("button",{type:"button",onClick:()=>R(!_),className:"text-white/60 hover:text-white transition-colors duration-200 p-1",tabIndex:-1,children:_?s.jsx(c.Z,{className:"w-5 h-5"}):s.jsx(d.Z,{className:"w-5 h-5"})}):v?s.jsx("button",{type:"button",onClick:w,className:"text-white/60 hover:text-white transition-colors duration-200 p-1",tabIndex:-1,children:v}):null}),x&&s.jsx("div",{className:`absolute top-1/2 transform -translate-y-1/2 ${k?"left-12":"right-12"} z-10`,children:z?s.jsx(m.Z,{className:"w-5 h-5 text-red-400"}):r&&!z?s.jsx(u.Z,{className:"w-5 h-5 text-green-400"}):null})]}),S&&j&&r&&s.jsx(i.motion.div,{className:"mt-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("div",{className:"flex-1 h-1 bg-white/20 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-300 ${(A=j.score)<2?"bg-red-500":A<3?"bg-yellow-500":A<4?"bg-blue-500":"bg-green-500"}`,style:{width:(T=j.score,`${T/4*100}%`)}})}),s.jsx("span",{className:`text-xs ${k?"font-tajawal":"font-sans"}`,style:{color:O.colors.text.secondary},children:j.label})]})}),z&&s.jsx(i.motion.div,{className:`mt-2 text-sm text-red-400 ${k?"text-right font-tajawal":"text-left font-sans"}`,initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:p})]})});h.displayName="AuthInput";let p=h;r()}catch(e){r(e)}})},7786:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>g});var s=t(997),o=t(6689),i=t(9442),l=t(8025),n=t(6197),c=t(5632),d=t(1675),m=t(5116),u=e([n]);n=(u.then?(await u)():u)[0];let g=({isOpen:e,onClose:a,title:t,subtitle:r,description:u,children:g,size:h="md",showCloseButton:p=!0,closeOnOverlayClick:x=!0,className:b=""})=>{let{currentTheme:E,themeName:f}=(0,d.Fg)(),N=(0,c.useRouter)(),{locale:v}=N,w="ar"===v,y="gold"===f;return(0,o.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),s.jsx(i.u,{appear:!0,show:e,as:o.Fragment,children:(0,s.jsxs)(l.V,{as:"div",className:"relative z-50",onClose:x?a:()=>{},dir:w?"rtl":"ltr",children:[s.jsx(i.u.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx("div",{className:"fixed inset-0",style:{background:"rgba(0, 0, 0, 0.8)",backdropFilter:"blur(8px)",WebkitBackdropFilter:"blur(8px)"}})}),s.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:s.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:s.jsx(i.u.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:s.jsx(l.V.Panel,{className:`
                  w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl"}[h]} transform overflow-hidden rounded-2xl
                  text-left align-middle shadow-xl transition-all relative
                  ${b}
                `,style:{background:y?"linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 20, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)":"linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 30, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)",backdropFilter:"blur(24px)",WebkitBackdropFilter:"blur(24px)",border:"1px solid rgba(255, 255, 255, 0.15)",boxShadow:y?"0 25px 50px -12px rgba(251, 191, 36, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)":"0 25px 50px -12px rgba(147, 51, 234, 0.25), 0 0 0 1px rgba(147, 51, 234, 0.1)"},children:(0,s.jsxs)(n.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.3},className:"relative",children:[p&&s.jsx(n.motion.button,{type:"button",onClick:a,className:`
                        absolute top-6 z-10 p-2 rounded-full transition-all duration-200
                        ${w?"left-6":"right-6"}
                        text-white/60 hover:text-white hover:bg-white/10
                        focus:outline-none focus:ring-2 focus:ring-white/20
                      `,whileHover:{scale:1.1},whileTap:{scale:.9},children:s.jsx(m.Z,{className:"w-6 h-6"})}),(0,s.jsxs)("div",{className:`px-8 pt-8 pb-6 ${w?"text-right":"text-left"}`,children:[s.jsx(l.V.Title,{as:"h3",className:`
                        text-3xl font-bold mb-3
                        ${w?"font-cairo text-arabic-premium":"font-sans"}
                      `,style:{color:E.colors.text.primary},children:t}),r&&s.jsx(n.motion.p,{className:`
                          text-lg font-medium mb-2
                          ${w?"font-tajawal":"font-sans"}
                        `,style:{color:E.colors.text.accent},initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},children:r}),u&&s.jsx(n.motion.p,{className:`
                          text-base leading-relaxed
                          ${w?"font-tajawal":"font-sans"}
                        `,style:{color:E.colors.text.secondary},initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:u})]}),s.jsx("div",{className:"px-8 pb-8",children:s.jsx(n.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.4},children:g})}),s.jsx("div",{className:"absolute top-0 left-0 w-full h-1 opacity-60",children:s.jsx("div",{className:"h-full rounded-t-2xl",style:{background:y?"linear-gradient(90deg, transparent 0%, rgba(251, 191, 36, 0.8) 50%, transparent 100%)":"linear-gradient(90deg, transparent 0%, rgba(147, 51, 234, 0.8) 50%, transparent 100%)"}})}),s.jsx("div",{className:"absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl overflow-hidden",children:s.jsx("div",{className:"absolute inset-0 opacity-20",style:{background:y?"linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.1) 50%, transparent 70%)":"linear-gradient(45deg, transparent 30%, rgba(147, 51, 234, 0.1) 50%, transparent 70%)",animation:"shimmer 3s ease-in-out infinite"}})})]})})})})})]})})};r()}catch(e){r(e)}})},4716:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>g});var s=t(997),o=t(6689),i=t.n(o),l=t(6197),n=t(5632),c=t(1377),d=t(1675),m=t(1649),u=e([l]);l=(u.then?(await u)():u)[0];let g=({mode:e,onLoading:a,onError:t,disabled:r=!1,className:o=""})=>{let[u,g]=i().useState(!1),{currentTheme:h,themeName:p}=(0,d.Fg)(),x=(0,n.useRouter)(),{locale:b}=x,{t:E}=(0,c.useTranslation)("auth"),f="gold"===p,N=async()=>{if(!r&&!u)try{g(!0),a?.(!0);let t=await (0,m.signIn)("google",{redirect:!1,callbackUrl:"signin"===e?"/dashboard":"/onboarding"});if(t?.error)throw Error(t.error)}catch(a){let e=a instanceof Error?a.message:E("login.networkError");t?.(e)}finally{g(!1),a?.(!1)}},v=`
    w-full h-14 px-6 rounded-xl font-semibold text-base
    flex items-center justify-center gap-3 group
    transition-all duration-300 ease-out
    border border-white/20 backdrop-blur-xl
    ${"ar"===b?"font-tajawal flex-row-reverse":"font-sans"}
    ${r||u?"opacity-50 cursor-not-allowed":"hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98] cursor-pointer"}
    ${o}
  `,w={background:f?"linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(126, 34, 206, 0.05) 100%)",color:h.colors.text.primary,boxShadow:f?"0 8px 32px rgba(251, 191, 36, 0.15)":"0 8px 32px rgba(147, 51, 234, 0.15)"};return s.jsx(l.motion.button,{type:"button",onClick:N,disabled:r||u,className:v,style:w,whileHover:r||u?{}:{background:f?"linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(126, 34, 206, 0.08) 100%)",boxShadow:f?"0 12px 40px rgba(251, 191, 36, 0.25)":"0 12px 40px rgba(147, 51, 234, 0.25)"},whileTap:r||u?{}:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:u?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),s.jsx("span",{children:E("modal.loading")})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"w-5 h-5 flex-shrink-0",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),s.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),s.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),s.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),s.jsx("span",{children:E("socialLogin.google")})]})})};r()}catch(e){r(e)}})},5392:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>N});var s=t(997),o=t(6689),i=t(6197),l=t(5632),n=t(1377),c=t(1675),d=t(8602),m=t(9926),u=t(7786),g=t(6809),h=t(4716),p=t(7330),x=t(666),b=t(2972),E=t(949),f=e([i,m,u,g,h]);[i,m,u,g,h]=f.then?(await f)():f;let N=({isOpen:e,onClose:a,onSuccess:t,onError:r,onSwitchToSignUp:f})=>{let[N,v]=(0,o.useState)({email:"",password:"",rememberMe:!1}),[w,y]=(0,o.useState)({}),[S,j]=(0,o.useState)({}),[I,T]=(0,o.useState)(!1),{t:A}=(0,n.useTranslation)(["auth","common"]),{currentTheme:_,themeName:R}=(0,c.Fg)(),C=(0,l.useRouter)(),{locale:D}=C,O="ar"===D,P="gold"===R,F=(e,a)=>{v(t=>({...t,[e]:a})),w[e]&&y(a=>({...a,[e]:""}))},L=e=>{j(a=>({...a,[e]:!0})),k(e)},k=e=>{try{"email"===e?d.userLoginSchema.shape.email.parse(N.email):"password"===e&&d.userLoginSchema.shape.password.parse(N.password),y(a=>({...a,[e]:""}))}catch(a){a instanceof m.z.ZodError&&y(t=>({...t,[e]:A(`auth:validation.${a.errors[0].code}`)||a.errors[0].message}))}},M=()=>{try{return d.userLoginSchema.parse(N),y({}),!0}catch(e){if(e instanceof m.z.ZodError){let a={};e.errors.forEach(e=>{e.path[0]&&(a[e.path[0]]=A(`auth:validation.${e.code}`)||e.message)}),y(a),j({email:!0,password:!0})}return!1}},z=async e=>{if(e.preventDefault(),M()){T(!0);try{await new Promise(e=>setTimeout(e,2e3)),t&&t({user:{email:N.email},token:"mock-token"}),a()}catch(a){let e=A("auth:login.networkError");y({general:e}),r&&r(e)}finally{T(!1)}}},$=`
    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300
    flex items-center justify-center gap-3 group relative overflow-hidden
    ${O?"font-tajawal":"font-sans"}
    ${I?"cursor-not-allowed opacity-70":"cursor-pointer"}
  `;return s.jsx(u.Z,{isOpen:e,onClose:a,title:A("auth:login.title"),subtitle:A("auth:login.subtitle"),description:A("auth:login.description"),size:"md",children:(0,s.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[w.general&&s.jsx(i.motion.div,{className:"p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},children:w.general}),s.jsx(g.Z,{label:A("auth:login.email"),placeholder:A("auth:login.emailPlaceholder"),type:"email",value:N.email,onChange:e=>F("email",e),onBlur:()=>L("email"),error:w.email,touched:S.email,required:!0,autoComplete:"email",leftIcon:s.jsx(p.Z,{className:"w-5 h-5"})}),s.jsx(g.Z,{label:A("auth:login.password"),placeholder:A("auth:login.passwordPlaceholder"),type:"password",value:N.password,onChange:e=>F("password",e),onBlur:()=>L("password"),error:w.password,touched:S.password,required:!0,autoComplete:"current-password",leftIcon:s.jsx(x.Z,{className:"w-5 h-5"})}),(0,s.jsxs)("div",{className:`flex items-center justify-between ${O?"flex-row-reverse":""}`,children:[(0,s.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer group",children:[s.jsx("input",{type:"checkbox",checked:N.rememberMe,onChange:e=>F("rememberMe",e.target.checked),className:"w-4 h-4 rounded border-white/20 bg-white/10 text-current focus:ring-2 focus:ring-current"}),s.jsx("span",{className:`text-sm ${O?"font-tajawal":"font-sans"}`,style:{color:_.colors.text.secondary},children:A("auth:login.rememberMe")})]}),s.jsx("button",{type:"button",className:`text-sm transition-colors duration-200 ${O?"font-tajawal":"font-sans"}`,style:{color:_.colors.text.accent},children:A("auth:login.forgotPassword")})]}),s.jsx(i.motion.button,{type:"submit",disabled:I,className:$,style:{background:P?"linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)",boxShadow:P?"0 8px 32px rgba(251, 191, 36, 0.3)":"0 8px 32px rgba(147, 51, 234, 0.3)"},whileHover:I?{}:{scale:1.02,y:-2},whileTap:I?{}:{scale:.98},children:I?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),A("auth:login.loggingIn")]}):(0,s.jsxs)(s.Fragment,{children:[A("auth:login.loginButton"),O?s.jsx(b.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"}):s.jsx(E.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-white/20"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:`px-4 ${O?"font-tajawal":"font-sans"}`,style:{backgroundColor:"rgba(0, 0, 0, 0.8)",color:_.colors.text.secondary},children:A("auth:socialLogin.continueWith")})})]}),s.jsx(h.Z,{mode:"signin",onLoading:e=>T(e),onError:e=>y(a=>({...a,general:e})),disabled:I}),(0,s.jsxs)("div",{className:`text-center ${O?"font-tajawal":"font-sans"}`,children:[s.jsx("span",{style:{color:_.colors.text.secondary},children:A("auth:login.noAccount")})," ",s.jsx("button",{type:"button",onClick:f,className:"font-semibold transition-colors duration-200 hover:underline",style:{color:_.colors.text.accent},children:A("auth:login.signUp")})]})]})})};r()}catch(e){r(e)}})},785:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>j});var s=t(997),o=t(6689),i=t(6197),l=t(5632),n=t(1377),c=t(1675),d=t(8602),m=t(9926),u=t(7786),g=t(6809),h=t(4716),p=t(3526),x=t(5374),b=t(7330),E=t(666),f=t(7767),N=t(924),v=t(7013),w=t(949),y=t(2972),S=e([i,m,u,g,h]);[i,m,u,g,h]=S.then?(await S)():S;let j=({isOpen:e,onClose:a,onSuccess:t,onError:r,onSwitchToSignIn:S})=>{let[j,I]=(0,o.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",role:"CLIENT",governorate:"",city:"",language:"ar",acceptTerms:!1}),[T,A]=(0,o.useState)({}),[_,R]=(0,o.useState)({}),[C,D]=(0,o.useState)(!1),[O,P]=(0,o.useState)(1),{t:F}=(0,n.useTranslation)(["auth","common"]),{currentTheme:L,themeName:k}=(0,c.Fg)(),M=(0,l.useRouter)(),{locale:z}=M,$="ar"===z,U="gold"===k,V=(0,o.useMemo)(()=>{let e=j.password,a=0,t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/[0-9]/.test(e),o=/[^A-Za-z0-9]/.test(e),i=e.length>=8;t&&a++,r&&a++,s&&a++,o&&a++,i&&a++;let l="weak";return a>=4?l="strong":a>=3?l="good":a>=2&&(l="fair"),{score:Math.min(a,4),label:l,hasUppercase:t,hasLowercase:r,hasNumber:s,hasSpecialChar:o,hasMinLength:i}},[j.password]),Z=(0,o.useMemo)(()=>{let e=p.S.find(e=>e.governorate===j.governorate);return e?e.cities:[]},[j.governorate]),B=(e,a)=>{I(t=>{let r={...t,[e]:a};return"governorate"===e&&(r.city=""),r}),T[e]&&A(a=>({...a,[e]:""}))},G=e=>{R(a=>({...a,[e]:!0})),Y(e)},Y=e=>{try{if("confirmPassword"===e){if(j.password!==j.confirmPassword)throw Error(F("auth:validation.confirmPassword"))}else{let a=d.userRegistrationSchema.shape[e];a&&a.parse(j[e])}A(a=>({...a,[e]:""}))}catch(a){a instanceof m.z.ZodError?A(t=>({...t,[e]:F(`auth:validation.${a.errors[0].code}`)||a.errors[0].message})):a instanceof Error&&A(t=>({...t,[e]:a.message}))}},W=e=>{let a=[];1===e?a.push("firstName","lastName","email","password","confirmPassword"):2===e&&(a.push("role"),j.phone&&a.push("phone"));let t=!0;return a.forEach(e=>{try{if("confirmPassword"===e){if(j.password!==j.confirmPassword)throw Error(F("auth:validation.confirmPassword"))}else{let a=d.userRegistrationSchema.shape[e];a&&a.parse(j[e])}A(a=>({...a,[e]:""}))}catch(a){t=!1,a instanceof m.z.ZodError?A(t=>({...t,[e]:F(`auth:validation.${a.errors[0].code}`)||a.errors[0].message})):a instanceof Error&&A(t=>({...t,[e]:a.message})),R(a=>({...a,[e]:!0}))}}),t},X=()=>{try{if(d.userRegistrationSchema.parse(j),j.password!==j.confirmPassword)return A(e=>({...e,confirmPassword:F("auth:validation.confirmPassword")})),!1;return A({}),!0}catch(e){if(e instanceof m.z.ZodError){let a={};e.errors.forEach(e=>{e.path[0]&&(a[e.path[0]]=F(`auth:validation.${e.code}`)||e.message)}),A(a);let t={};Object.keys(j).forEach(e=>{t[e]=!0}),R(t)}return!1}},H=async e=>{if(e.preventDefault(),X()){D(!0);try{await new Promise(e=>setTimeout(e,2e3)),t&&t({user:{email:j.email,role:j.role},token:"mock-token"}),a()}catch(a){let e=F("auth:register.networkError");A({general:e}),r&&r(e)}finally{D(!1)}}},q=`
    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300
    flex items-center justify-center gap-3 group relative overflow-hidden
    ${$?"font-tajawal":"font-sans"}
    ${C?"cursor-not-allowed opacity-70":"cursor-pointer"}
  `,K={background:U?"linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)",boxShadow:U?"0 8px 32px rgba(251, 191, 36, 0.3)":"0 8px 32px rgba(147, 51, 234, 0.3)"};return s.jsx(u.Z,{isOpen:e,onClose:a,title:F("auth:register.title"),subtitle:F("auth:register.subtitle"),description:F("auth:register.description"),size:"lg",children:(0,s.jsxs)("form",{onSubmit:H,className:"space-y-6",children:[s.jsx("div",{className:"flex items-center justify-center mb-8",children:s.jsx("div",{className:"flex items-center gap-4",children:[1,2,3].map(e=>(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                    transition-all duration-300
                    ${e<=O?"text-white":"text-white/40"}
                  `,style:{background:e<=O?U?"linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9))":"linear-gradient(135deg, rgba(147, 51, 234, 0.9), rgba(126, 34, 206, 0.9))":"rgba(255, 255, 255, 0.1)",border:"1px solid rgba(255, 255, 255, 0.2)"},children:e}),e<3&&s.jsx("div",{className:`w-8 h-0.5 mx-2 transition-all duration-300 ${e<O?"opacity-100":"opacity-30"}`,style:{background:e<O?U?"rgba(251, 191, 36, 0.6)":"rgba(147, 51, 234, 0.6)":"rgba(255, 255, 255, 0.2)"}})]},e))})}),T.general&&s.jsx(i.motion.div,{className:"p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},children:T.general}),(0,s.jsxs)(i.motion.div,{initial:{opacity:0,x:$?-20:20},animate:{opacity:1,x:0},exit:{opacity:0,x:$?20:-20},transition:{duration:.3},children:[1===O&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(g.Z,{label:F("auth:register.firstName"),placeholder:F("auth:register.firstNamePlaceholder"),value:j.firstName,onChange:e=>B("firstName",e),onBlur:()=>G("firstName"),error:T.firstName,touched:_.firstName,required:!0,leftIcon:s.jsx(x.Z,{className:"w-5 h-5"})}),s.jsx(g.Z,{label:F("auth:register.lastName"),placeholder:F("auth:register.lastNamePlaceholder"),value:j.lastName,onChange:e=>B("lastName",e),onBlur:()=>G("lastName"),error:T.lastName,touched:_.lastName,required:!0,leftIcon:s.jsx(x.Z,{className:"w-5 h-5"})})]}),s.jsx(g.Z,{label:F("auth:register.email"),placeholder:F("auth:register.emailPlaceholder"),type:"email",value:j.email,onChange:e=>B("email",e),onBlur:()=>G("email"),error:T.email,touched:_.email,required:!0,autoComplete:"email",leftIcon:s.jsx(b.Z,{className:"w-5 h-5"})}),s.jsx(g.Z,{label:F("auth:register.password"),placeholder:F("auth:register.passwordPlaceholder"),type:"password",value:j.password,onChange:e=>B("password",e),onBlur:()=>G("password"),error:T.password,touched:_.password,required:!0,autoComplete:"new-password",leftIcon:s.jsx(E.Z,{className:"w-5 h-5"}),showPasswordStrength:!0,passwordStrength:{score:V.score,label:F(`auth:passwordStrength.${V.label}`)}}),s.jsx(g.Z,{label:F("auth:register.confirmPassword"),placeholder:F("auth:register.confirmPasswordPlaceholder"),type:"password",value:j.confirmPassword,onChange:e=>B("confirmPassword",e),onBlur:()=>G("confirmPassword"),error:T.confirmPassword,touched:_.confirmPassword,required:!0,autoComplete:"new-password",leftIcon:s.jsx(E.Z,{className:"w-5 h-5"})})]}),2===O&&(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(g.Z,{label:F("auth:register.phone"),placeholder:F("auth:register.phonePlaceholder"),type:"tel",value:j.phone||"",onChange:e=>B("phone",e),onBlur:()=>G("phone"),error:T.phone,touched:_.phone,leftIcon:s.jsx(f.Z,{className:"w-5 h-5"})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:`block text-lg font-semibold ${$?"text-right font-tajawal":"text-left font-sans"}`,style:{color:L.colors.text.primary},children:[F("auth:register.role")," ",s.jsx("span",{className:"text-red-400",children:"*"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)(i.motion.label,{className:`
                      relative p-6 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${"CLIENT"===j.role?U?"border-amber-400/50":"border-purple-400/50":"border-white/20"}
                    `,style:{background:"CLIENT"===j.role?U?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("input",{type:"radio",name:"role",value:"CLIENT",checked:"CLIENT"===j.role,onChange:e=>B("role",e.target.value),className:"sr-only"}),(0,s.jsxs)("div",{className:`flex items-center gap-4 ${$?"flex-row-reverse text-right":"text-left"}`,children:[s.jsx(N.Z,{className:"w-8 h-8 text-blue-400"}),s.jsx("div",{children:s.jsx("div",{className:`font-semibold text-lg ${$?"font-tajawal":"font-sans"}`,style:{color:L.colors.text.primary},children:F("auth:register.client")})})]})]}),(0,s.jsxs)(i.motion.label,{className:`
                      relative p-6 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${"EXPERT"===j.role?U?"border-amber-400/50":"border-purple-400/50":"border-white/20"}
                    `,style:{background:"EXPERT"===j.role?U?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("input",{type:"radio",name:"role",value:"EXPERT",checked:"EXPERT"===j.role,onChange:e=>B("role",e.target.value),className:"sr-only"}),(0,s.jsxs)("div",{className:`flex items-center gap-4 ${$?"flex-row-reverse text-right":"text-left"}`,children:[s.jsx(v.Z,{className:"w-8 h-8 text-green-400"}),s.jsx("div",{children:s.jsx("div",{className:`font-semibold text-lg ${$?"font-tajawal":"font-sans"}`,style:{color:L.colors.text.primary},children:F("auth:register.expert")})})]})]})]}),T.role&&_.role&&s.jsx("div",{className:`text-sm text-red-400 ${$?"text-right font-tajawal":"text-left font-sans"}`,children:T.role})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{className:`block text-sm font-medium ${$?"text-right font-tajawal":"text-left font-sans"}`,style:{color:L.colors.text.secondary},children:F("auth:register.governorate")}),(0,s.jsxs)("select",{value:j.governorate,onChange:e=>B("governorate",e.target.value),onBlur:()=>G("governorate"),className:`
                      w-full h-14 px-4 rounded-xl transition-all duration-300
                      ${$?"text-right font-tajawal":"text-left font-sans"}
                      text-white bg-white/8 border border-white/20
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      ${U?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50"}
                    `,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},children:[s.jsx("option",{value:"",children:F("auth:register.governoratePlaceholder")}),p.S.map(e=>s.jsx("option",{value:e.governorate,className:"bg-gray-800",children:e.governorate},e.governorate))]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{className:`block text-sm font-medium ${$?"text-right font-tajawal":"text-left font-sans"}`,style:{color:L.colors.text.secondary},children:F("auth:register.city")}),(0,s.jsxs)("select",{value:j.city,onChange:e=>B("city",e.target.value),onBlur:()=>G("city"),disabled:!j.governorate,className:`
                      w-full h-14 px-4 rounded-xl transition-all duration-300
                      ${$?"text-right font-tajawal":"text-left font-sans"}
                      text-white bg-white/8 border border-white/20
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      disabled:opacity-50 disabled:cursor-not-allowed
                      ${U?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50"}
                    `,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},children:[s.jsx("option",{value:"",children:F("auth:register.cityPlaceholder")}),Z.map(e=>s.jsx("option",{value:e,className:"bg-gray-800",children:e},e))]})]})]})]}),3===O&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("label",{className:`block text-lg font-semibold ${$?"text-right font-tajawal":"text-left font-sans"}`,style:{color:L.colors.text.primary},children:F("auth:register.language")}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)(i.motion.label,{className:`
                      relative p-4 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${"ar"===j.language?U?"border-amber-400/50":"border-purple-400/50":"border-white/20"}
                    `,style:{background:"ar"===j.language?U?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("input",{type:"radio",name:"language",value:"ar",checked:"ar"===j.language,onChange:e=>B("language",e.target.value),className:"sr-only"}),s.jsx("div",{className:"text-center",children:s.jsx("div",{className:`font-semibold ${$?"font-tajawal":"font-sans"}`,style:{color:L.colors.text.primary},children:F("auth:register.arabic")})})]}),(0,s.jsxs)(i.motion.label,{className:`
                      relative p-4 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${"en"===j.language?U?"border-amber-400/50":"border-purple-400/50":"border-white/20"}
                    `,style:{background:"en"===j.language?U?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("input",{type:"radio",name:"language",value:"en",checked:"en"===j.language,onChange:e=>B("language",e.target.value),className:"sr-only"}),s.jsx("div",{className:"text-center",children:s.jsx("div",{className:`font-semibold ${$?"font-tajawal":"font-sans"}`,style:{color:L.colors.text.primary},children:F("auth:register.english")})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer group",children:[s.jsx("input",{type:"checkbox",checked:j.acceptTerms,onChange:e=>B("acceptTerms",e.target.checked),className:`
                      mt-1 w-5 h-5 rounded border-white/20 bg-white/10
                      focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      ${U?"text-amber-400 focus:ring-amber-400/50":"text-purple-400 focus:ring-purple-400/50"}
                    `}),(0,s.jsxs)("span",{className:`text-sm leading-relaxed ${$?"font-tajawal text-right":"font-sans text-left"}`,style:{color:L.colors.text.secondary},children:[F("auth:register.acceptTerms")," ",s.jsx("button",{type:"button",className:"font-semibold transition-colors duration-200 hover:underline",style:{color:L.colors.text.accent},children:F("auth:register.termsOfService")})," ",F("auth:register.and")," ",s.jsx("button",{type:"button",className:"font-semibold transition-colors duration-200 hover:underline",style:{color:L.colors.text.accent},children:F("auth:register.privacyPolicy")})]})]}),T.acceptTerms&&_.acceptTerms&&s.jsx("div",{className:`text-sm text-red-400 ${$?"text-right font-tajawal":"text-left font-sans"}`,children:T.acceptTerms})]})]})]},O),(0,s.jsxs)("div",{className:`flex gap-4 ${1===O?"justify-end":"justify-between"}`,children:[O>1&&(0,s.jsxs)(i.motion.button,{type:"button",onClick:()=>{P(e=>e-1)},className:q,style:{background:"rgba(255, 255, 255, 0.08)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)"},whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},children:[$?s.jsx(w.Z,{className:"w-5 h-5"}):s.jsx(y.Z,{className:"w-5 h-5"}),F("common:actions.previous")]}),O<3?(0,s.jsxs)(i.motion.button,{type:"button",onClick:()=>{W(O)&&P(e=>e+1)},className:q,style:K,whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},children:[F("common:actions.next"),$?s.jsx(y.Z,{className:"w-5 h-5"}):s.jsx(w.Z,{className:"w-5 h-5"})]}):s.jsx(i.motion.button,{type:"submit",disabled:C,className:q,style:K,whileHover:C?{}:{scale:1.02,y:-2},whileTap:C?{}:{scale:.98},children:C?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),F("auth:register.registering")]}):(0,s.jsxs)(s.Fragment,{children:[F("auth:register.registerButton"),$?s.jsx(y.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"}):s.jsx(w.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})})]}),1===O&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-white/20"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:`px-4 ${$?"font-tajawal":"font-sans"}`,style:{backgroundColor:L.colors.neutral[500],color:L.colors.text.secondary},children:F("auth:socialLogin.continueWith")})})]}),s.jsx(h.Z,{mode:"signup",onLoading:e=>D(e),onError:e=>A(a=>({...a,general:e})),disabled:C})]}),(0,s.jsxs)("div",{className:`text-center ${$?"font-tajawal":"font-sans"}`,children:[s.jsx("span",{style:{color:L.colors.text.secondary},children:F("auth:register.haveAccount")})," ",s.jsx("button",{type:"button",onClick:S,className:"font-semibold transition-colors duration-200 hover:underline",style:{color:L.colors.text.accent},children:F("auth:register.signIn")})]})]})})};r()}catch(e){r(e)}})},479:(e,a,t)=>{t.r(a),t.d(a,{default:()=>Document});var r=t(997),s=t(331);function Document(){return(0,r.jsxs)(s.Html,{children:[(0,r.jsxs)(s.Head,{children:[r.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),r.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),r.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),r.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),r.jsx("link",{rel:"manifest",href:"/site.webmanifest"}),r.jsx("meta",{name:"theme-color",content:"#0ea5e9"}),r.jsx("meta",{name:"msapplication-TileColor",content:"#0ea5e9"}),r.jsx("meta",{property:"og:type",content:"website"}),r.jsx("meta",{property:"og:site_name",content:"Freela Syria"}),r.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),r.jsx("meta",{name:"twitter:site",content:"@freela_syria"}),r.jsx("meta",{name:"format-detection",content:"telephone=no"}),r.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),r.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),r.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"})]}),(0,r.jsxs)("body",{className:"antialiased",children:[r.jsx(s.Main,{}),r.jsx(s.NextScript,{})]})]})}},3526:(e,a,t)=>{t.d(a,{S:()=>r});let r=[{governorate:"دمشق",cities:["دمشق","جرمانا","دوما","داريا","قدسيا","صحنايا"]},{governorate:"حلب",cities:["حلب","منبج","عفرين","جرابلس","الباب","أعزاز"]},{governorate:"حمص",cities:["حمص","تدمر","القريتين","الرستن","تلبيسة","صدد"]},{governorate:"حماة",cities:["حماة","سلمية","مصياف","محردة","السقيلبية","قلعة المضيق"]},{governorate:"اللاذقية",cities:["اللاذقية","جبلة","بانياس","القرداحة","الحفة","كسب"]},{governorate:"طرطوس",cities:["طرطوس","بانياس","صافيتا","دريكيش","الشيخ بدر","القدموس"]},{governorate:"إدلب",cities:["إدلب","جسر الشغور","أريحا","معرة النعمان","سراقب","خان شيخون"]},{governorate:"درعا",cities:["درعا","إزرع","الصنمين","نوى","جاسم","الشيخ مسكين"]},{governorate:"السويداء",cities:["السويداء","صلخد","شهبا","قنوات","المزرعة","عرى"]},{governorate:"القنيطرة",cities:["القنيطرة","فيق","خان أرنبة","مسعدة","جباتا الخشب","عين التينة"]},{governorate:"الرقة",cities:["الرقة","تل أبيض","الثورة","سلوك","كسرة","الجرنية"]},{governorate:"دير الزور",cities:["دير الزور","الميادين","البوكمال","الأشارة","التيم","الصالحية"]},{governorate:"الحسكة",cities:["الحسكة","القامشلي","رأس العين","المالكية","عامودا","الدرباسية"]},{governorate:"ريف دمشق",cities:["التل","النبك","يبرود","القطيفة","الزبداني","مضايا"]}]},308:function(e,a,t){var r=this&&this.__createBinding||(Object.create?function(e,a,t,r){void 0===r&&(r=t);var s=Object.getOwnPropertyDescriptor(a,t);(!s||("get"in s?!a.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return a[t]}}),Object.defineProperty(e,r,s)}:function(e,a,t,r){void 0===r&&(r=t),e[r]=a[t]}),s=this&&this.__exportStar||function(e,a){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(a,t)||r(a,e,t)};Object.defineProperty(a,"__esModule",{value:!0}),a.sanitizeInput=a.isStrongPassword=a.normalizeEmail=a.isValidEmail=a.generateNumericCode=a.generateRandomString=void 0,s(t(6164),a),a.generateRandomString=(e=32)=>{let a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let r=0;r<e;r++)t+=a.charAt(Math.floor(Math.random()*a.length));return t},a.generateNumericCode=(e=6)=>{let a="0123456789",t="";for(let r=0;r<e;r++)t+=a.charAt(Math.floor(Math.random()*a.length));return t},a.isValidEmail=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),a.normalizeEmail=e=>e.toLowerCase().trim(),a.isStrongPassword=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(e),a.sanitizeInput=e=>e.trim().replace(/[<>]/g,"")},8812:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.ERROR_CODES=a.NOTIFICATION_TYPES=a.PASSWORD_RESET=a.EMAIL_VERIFICATION=a.SESSION_CONFIG=a.CACHE_TTL=a.RATE_LIMITS=a.TEXT_LIMITS=a.REVISION_LIMITS=a.DELIVERY_TIME_RANGES=a.PRICE_RANGES=a.RATING_SCALE=a.PAGINATION_DEFAULTS=a.FILE_SIZE_LIMITS=a.ALLOWED_VIDEO_TYPES=a.ALLOWED_DOCUMENT_TYPES=a.ALLOWED_IMAGE_TYPES=a.LANGUAGES=a.CURRENCIES=a.PAYMENT_METHODS=a.PAYMENT_STATUSES=a.BOOKING_STATUSES=a.SERVICE_STATUSES=a.USER_STATUSES=a.USER_ROLES=a.SERVICE_CATEGORIES_EN=a.SERVICE_CATEGORIES=a.SYRIAN_GOVERNORATES_EN=a.SYRIAN_GOVERNORATES=void 0,a.SYRIAN_GOVERNORATES=["دمشق","ريف دمشق","حلب","حمص","حماة","اللاذقية","إدلب","الحسكة","دير الزور","الرقة","درعا","السويداء","القنيطرة","طرطوس"],a.SYRIAN_GOVERNORATES_EN=["Damascus","Damascus Countryside","Aleppo","Homs","Hama","Latakia","Idlib","Al-Hasakah","Deir ez-Zor","Raqqa","Daraa","As-Suwayda","Quneitra","Tartus"],a.SERVICE_CATEGORIES=["تطوير البرمجيات","التصميم الجرافيكي","التسويق الرقمي","الكتابة والترجمة","التصوير والفيديو","الاستشارات","التعليم والتدريب","الخدمات المالية","الهندسة والعمارة","الطب والصحة"],a.SERVICE_CATEGORIES_EN=["Software Development","Graphic Design","Digital Marketing","Writing & Translation","Photography & Video","Consulting","Education & Training","Financial Services","Engineering & Architecture","Medicine & Health"],a.USER_ROLES=["CLIENT","EXPERT","ADMIN"],a.USER_STATUSES=["ACTIVE","INACTIVE","SUSPENDED","PENDING_VERIFICATION"],a.SERVICE_STATUSES=["DRAFT","PENDING_REVIEW","ACTIVE","PAUSED","REJECTED","ARCHIVED"],a.BOOKING_STATUSES=["PENDING","ACCEPTED","IN_PROGRESS","DELIVERED","REVISION_REQUESTED","COMPLETED","CANCELLED","DISPUTED","REFUNDED"],a.PAYMENT_STATUSES=["PENDING","PROCESSING","COMPLETED","FAILED","CANCELLED","REFUNDED","DISPUTED","CHARGEBACK"],a.PAYMENT_METHODS=["CREDIT_CARD","DEBIT_CARD","PAYPAL","BANK_TRANSFER","MOBILE_WALLET","CRYPTOCURRENCY","CASH"],a.CURRENCIES=["USD","SYP"],a.LANGUAGES=["ar","en"],a.ALLOWED_IMAGE_TYPES=["image/jpeg","image/jpg","image/png","image/gif","image/webp"],a.ALLOWED_DOCUMENT_TYPES=["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"],a.ALLOWED_VIDEO_TYPES=["video/mp4","video/avi","video/mov","video/wmv","video/webm"],a.FILE_SIZE_LIMITS={AVATAR:5242880,PORTFOLIO:********,DOCUMENT:********,VIDEO:*********},a.PAGINATION_DEFAULTS={PAGE:1,LIMIT:10,MAX_LIMIT:100},a.RATING_SCALE={MIN:1,MAX:5},a.PRICE_RANGES={MIN_USD:5,MAX_USD:1e4,MIN_SYP:1e4,MAX_SYP:2e7},a.DELIVERY_TIME_RANGES={MIN:1,MAX:365},a.REVISION_LIMITS={MIN:0,MAX:10},a.TEXT_LIMITS={TITLE_MIN:10,TITLE_MAX:100,DESCRIPTION_MIN:50,DESCRIPTION_MAX:2e3,BIO_MAX:500,MESSAGE_MAX:2e3,REVIEW_TITLE_MAX:100,REVIEW_COMMENT_MAX:1e3},a.RATE_LIMITS={GENERAL:{WINDOW_MS:9e5,MAX_REQUESTS:100},AUTH:{WINDOW_MS:9e5,MAX_REQUESTS:5},UPLOAD:{WINDOW_MS:6e4,MAX_REQUESTS:10}},a.CACHE_TTL={USER_DATA:3600,SERVICE_DATA:1800,CATEGORY_DATA:86400,SEARCH_RESULTS:300},a.SESSION_CONFIG={ACCESS_TOKEN_EXPIRES:900,REFRESH_TOKEN_EXPIRES:604800,REMEMBER_ME_EXPIRES:2592e3},a.EMAIL_VERIFICATION={TOKEN_EXPIRES:86400,RESEND_COOLDOWN:300},a.PASSWORD_RESET={TOKEN_EXPIRES:3600,REQUEST_COOLDOWN:300},a.NOTIFICATION_TYPES=["BOOKING_CREATED","BOOKING_ACCEPTED","BOOKING_REJECTED","BOOKING_COMPLETED","BOOKING_CANCELLED","PAYMENT_RECEIVED","PAYMENT_FAILED","MESSAGE_RECEIVED","REVIEW_RECEIVED","SERVICE_APPROVED","SERVICE_REJECTED","ACCOUNT_VERIFIED","ACCOUNT_SUSPENDED"],a.ERROR_CODES={INVALID_CREDENTIALS:"INVALID_CREDENTIALS",TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",SESSION_EXPIRED:"SESSION_EXPIRED",INSUFFICIENT_PERMISSIONS:"INSUFFICIENT_PERMISSIONS",ACCESS_DENIED:"ACCESS_DENIED",VALIDATION_ERROR:"VALIDATION_ERROR",REQUIRED_FIELD:"REQUIRED_FIELD",INVALID_FORMAT:"INVALID_FORMAT",NOT_FOUND:"NOT_FOUND",ALREADY_EXISTS:"ALREADY_EXISTS",DUPLICATE_ENTRY:"DUPLICATE_ENTRY",RATE_LIMIT_EXCEEDED:"RATE_LIMIT_EXCEEDED",FILE_TOO_LARGE:"FILE_TOO_LARGE",INVALID_FILE_TYPE:"INVALID_FILE_TYPE",PAYMENT_FAILED:"PAYMENT_FAILED",INSUFFICIENT_FUNDS:"INSUFFICIENT_FUNDS",INTERNAL_ERROR:"INTERNAL_ERROR",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE"}},8602:function(e,a,t){var r=this&&this.__createBinding||(Object.create?function(e,a,t,r){void 0===r&&(r=t);var s=Object.getOwnPropertyDescriptor(a,t);(!s||("get"in s?!a.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return a[t]}}),Object.defineProperty(e,r,s)}:function(e,a,t,r){void 0===r&&(r=t),e[r]=a[t]}),s=this&&this.__exportStar||function(e,a){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(a,t)||r(a,e,t)};Object.defineProperty(a,"__esModule",{value:!0}),s(t(6164),a),s(t(8812),a),s(t(308),a)},6164:(e,a,t)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.validateAudioFile=a.validateVideoFile=a.validateDocumentFile=a.validateImageFile=a.validateFileType=a.validateExpiryDate=a.validateCVV=a.validateCreditCard=a.validateSyrianPhone=a.validateEnglishText=a.validateArabicText=a.validateSlug=a.validateUrl=a.validatePhone=a.validatePassword=a.validateEmail=a.fileUploadSchema=a.reviewSchema=a.messageSchema=a.bookingSchema=a.serviceSchema=a.userProfileSchema=a.userLoginSchema=a.userRegistrationSchema=a.slugSchema=a.urlSchema=a.phoneSchema=a.passwordSchema=a.emailSchema=void 0;let r=t(8316);a.emailSchema=r.z.string().email("Invalid email address"),a.passwordSchema=r.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),a.phoneSchema=r.z.string().regex(/^(\+963|0)?[0-9]{8,9}$/,"Invalid Syrian phone number"),a.urlSchema=r.z.string().url("Invalid URL"),a.slugSchema=r.z.string().regex(/^[a-z0-9-]+$/,"Slug can only contain lowercase letters, numbers, and hyphens").min(3,"Slug must be at least 3 characters").max(50,"Slug must be at most 50 characters"),a.userRegistrationSchema=r.z.object({email:a.emailSchema,password:a.passwordSchema,firstName:r.z.string().min(2,"First name must be at least 2 characters"),lastName:r.z.string().min(2,"Last name must be at least 2 characters"),phone:a.phoneSchema.optional(),role:r.z.enum(["CLIENT","EXPERT","client","expert"]).transform(e=>e.toUpperCase()),language:r.z.enum(["ar","en"]).default("ar"),acceptTerms:r.z.boolean().refine(e=>!0===e,"You must accept the terms")}),a.userLoginSchema=r.z.object({email:a.emailSchema,password:r.z.string().min(1,"Password is required"),rememberMe:r.z.boolean().optional()}),a.userProfileSchema=r.z.object({firstName:r.z.string().min(2,"First name must be at least 2 characters"),lastName:r.z.string().min(2,"Last name must be at least 2 characters"),phone:a.phoneSchema.optional(),bio:r.z.string().max(500,"Bio must be at most 500 characters").optional(),website:a.urlSchema.optional(),location:r.z.object({governorate:r.z.string(),city:r.z.string(),district:r.z.string().optional()}).optional()}),a.serviceSchema=r.z.object({title:r.z.object({ar:r.z.string().min(10,"Arabic title must be at least 10 characters"),en:r.z.string().min(10,"English title must be at least 10 characters").optional()}),description:r.z.object({ar:r.z.string().min(50,"Arabic description must be at least 50 characters"),en:r.z.string().min(50,"English description must be at least 50 characters").optional()}),categoryId:r.z.string().min(1,"Category is required"),tags:r.z.array(r.z.string()).min(1,"At least one tag is required").max(10,"Maximum 10 tags allowed"),pricing:r.z.object({type:r.z.enum(["fixed","hourly","package","custom"]),basePrice:r.z.number().min(5,"Minimum price is $5"),currency:r.z.enum(["USD","SYP"]).default("USD")}),deliveryTime:r.z.number().min(1,"Delivery time must be at least 1 day").max(365,"Maximum delivery time is 365 days"),revisions:r.z.number().min(0,"Revisions cannot be negative").max(10,"Maximum 10 revisions")}),a.bookingSchema=r.z.object({serviceId:r.z.string().min(1,"Service is required"),packageId:r.z.string().optional(),requirements:r.z.array(r.z.object({questionId:r.z.string(),answer:r.z.union([r.z.string(),r.z.array(r.z.string())])})),customInstructions:r.z.string().max(1e3,"Instructions must be at most 1000 characters").optional(),preferredDeliveryDate:r.z.date().optional()}),a.messageSchema=r.z.object({content:r.z.string().min(1,"Message cannot be empty").max(2e3,"Message must be at most 2000 characters"),type:r.z.enum(["text","file","image"]).default("text")}),a.reviewSchema=r.z.object({rating:r.z.number().min(1,"Rating must be at least 1").max(5,"Rating must be at most 5"),title:r.z.string().max(100,"Title must be at most 100 characters").optional(),comment:r.z.string().max(1e3,"Comment must be at most 1000 characters").optional(),wouldRecommend:r.z.boolean()}),a.fileUploadSchema=r.z.object({filename:r.z.string().min(1,"Filename is required"),mimeType:r.z.string().min(1,"MIME type is required"),size:r.z.number().max(********,"File size must be less than 10MB")}),a.validateEmail=e=>a.emailSchema.safeParse(e).success,a.validatePassword=e=>a.passwordSchema.safeParse(e).success,a.validatePhone=e=>a.phoneSchema.safeParse(e).success,a.validateUrl=e=>a.urlSchema.safeParse(e).success,a.validateSlug=e=>a.slugSchema.safeParse(e).success,a.validateArabicText=e=>/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(e),a.validateEnglishText=e=>/^[a-zA-Z\s.,!?'"()-]+$/.test(e),a.validateSyrianPhone=e=>/^(\+963|0)?[0-9]{8,9}$/.test(e),a.validateCreditCard=e=>{let a=e.replace(/\s/g,"");if(!/^[0-9]{13,19}$/.test(a))return!1;let t=0,r=!1;for(let e=a.length-1;e>=0;e--){let s=parseInt(a[e]);r&&(s*=2)>9&&(s-=9),t+=s,r=!r}return t%10==0},a.validateCVV=(e,a)=>"amex"===a?/^[0-9]{4}$/.test(e):/^[0-9]{3}$/.test(e),a.validateExpiryDate=(e,a)=>{let t=new Date,r=t.getFullYear(),s=t.getMonth()+1,o=parseInt(e),i=parseInt(a);return!(o<1)&&!(o>12)&&!(i<r)&&(i!==r||!(o<s))},a.validateFileType=(e,a)=>{let t=e.split(".").pop()?.toLowerCase();return!!t&&a.includes(t)},a.validateImageFile=e=>(0,a.validateFileType)(e,["jpg","jpeg","png","gif","webp","svg"]),a.validateDocumentFile=e=>(0,a.validateFileType)(e,["pdf","doc","docx","txt","rtf"]),a.validateVideoFile=e=>(0,a.validateFileType)(e,["mp4","avi","mov","wmv","flv","webm"]),a.validateAudioFile=e=>(0,a.validateFileType)(e,["mp3","wav","ogg","aac","flac"])}};