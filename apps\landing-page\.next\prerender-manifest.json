{"version": 4, "routes": {"/ar/test-auth": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/2ThmRerwEgCjWr2i_tX7j/test-auth.json"}, "/en/test-auth": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/2ThmRerwEgCjWr2i_tX7j/test-auth.json"}, "/ar": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/2ThmRerwEgCjWr2i_tX7j/index.json"}, "/en": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/2ThmRerwEgCjWr2i_tX7j/index.json"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "f3a4b5febb40b9e2bc1d68c39be6eb08", "previewModeSigningKey": "d9d506e1d306d3cbcad9e49d6ee0a46dd47ea9d4aa1c45bbf166395012783f47", "previewModeEncryptionKey": "33cdc057afb0bdc55df6b5d1eb723e06bab86f02d86f655afcd43ee3bc4e7bff"}}