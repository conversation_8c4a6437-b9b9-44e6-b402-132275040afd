exports.id=801,exports.ids=[801],exports.modules={4696:(e,a,r)=>{"use strict";r.d(a,{Z:()=>o});var t=r(997),s=r(6689);class i extends s.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,a){let r=e.message?.includes("Frame with ID")||e.message?.includes("Could not establish connection")||e.message?.includes("MetaMask")||e.message?.includes("chrome-extension")||e.message?.includes("contentscript")||e.message?.includes("serviceWorker");r||console.error("<PERSON>rror<PERSON>ou<PERSON><PERSON> caught an error:",e,a)}render(){if(this.state.hasError){let e=this.state.error?.message?.includes("Frame with ID")||this.state.error?.message?.includes("Could not establish connection")||this.state.error?.message?.includes("MetaMask")||this.state.error?.message?.includes("chrome-extension")||this.state.error?.message?.includes("contentscript")||this.state.error?.message?.includes("serviceWorker");return e?this.props.children:this.props.fallback||t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Something went wrong"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"We're sorry, but something unexpected happened. Please refresh the page."}),t.jsx("button",{type:"button",onClick:()=>window.location.reload(),className:"btn-primary",children:"Refresh Page"})]})})}return this.props.children}}let o=i},3807:(e,a,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(a,{Z:()=>p});var s=r(997),i=r(6689),o=r(6197),n=r(1675),d=r(6752),l=r(5116),c=r(6763),m=r(5095),h=r(8131),g=e([o]);o=(g.then?(await g)():g)[0];let p=({showInProduction:e=!1,position:a="bottom-right"})=>{let{switchTheme:r,isGoldTheme:t,isPurpleTheme:g}=(0,n.Fg)(),[p,x]=(0,i.useState)(!1),[b,u]=(0,i.useState)(!1);if((0,i.useEffect)(()=>{u(e)},[e]),!b)return null;let f=e=>{r(e),setTimeout(()=>x(!1),500)};return(0,s.jsxs)("div",{className:`fixed ${{"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","top-right":"top-6 right-6","top-left":"top-6 left-6"}[a]} z-[9999] select-none`,children:[s.jsx(o.AnimatePresence,{children:p&&(0,s.jsxs)(o.motion.div,{initial:{opacity:0,y:20,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.9},transition:{duration:.2,ease:"easeOut"},className:"mb-4 p-4 rounded-2xl overflow-hidden",style:{background:"linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)",backdropFilter:"blur(25px)",WebkitBackdropFilter:"blur(25px)",border:"1px solid rgba(255, 255, 255, 0.1)",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.5)"},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(d.Z,{className:"w-5 h-5 text-white"}),s.jsx("span",{className:"text-white font-semibold text-sm",children:"Theme Controller"})]}),s.jsx("button",{onClick:()=>x(!1),className:"text-white/60 hover:text-white transition-colors p-1",children:s.jsx(l.Z,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(o.motion.button,{onClick:()=>f("gold"),className:`w-full p-3 rounded-xl transition-all duration-300 group ${t?"ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-500/20 to-orange-500/20":"hover:bg-white/5"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",boxShadow:"0 4px 12px rgba(255, 215, 0, 0.3)"}}),t&&s.jsx("div",{className:"absolute -top-1 -right-1",children:s.jsx(c.Z,{className:"w-4 h-4 text-yellow-400"})})]}),(0,s.jsxs)("div",{className:"flex-1 text-left",children:[s.jsx("div",{className:"text-white font-medium text-sm",children:"Gold Premium"}),s.jsx("div",{className:"text-white/60 text-xs",children:"Luxury & Elegance"})]}),t&&s.jsx("div",{className:"text-yellow-400",children:s.jsx(m.Z,{className:"w-4 h-4"})})]})}),s.jsx(o.motion.button,{onClick:()=>f("purple"),className:`w-full p-3 rounded-xl transition-all duration-300 group ${g?"ring-2 ring-purple-400 bg-gradient-to-r from-purple-500/20 to-blue-500/20":"hover:bg-white/5"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:"0 4px 12px rgba(217, 70, 239, 0.3)"}}),g&&s.jsx("div",{className:"absolute -top-1 -right-1",children:s.jsx(c.Z,{className:"w-4 h-4 text-purple-400"})})]}),(0,s.jsxs)("div",{className:"flex-1 text-left",children:[s.jsx("div",{className:"text-white font-medium text-sm",children:"Purple Dark"}),s.jsx("div",{className:"text-white/60 text-xs",children:"Modern & Professional"})]}),g&&s.jsx("div",{className:"text-purple-400",children:s.jsx(m.Z,{className:"w-4 h-4"})})]})})]}),s.jsx("div",{className:"mt-4 pt-3 border-t border-white/10",children:(0,s.jsxs)("div",{className:"text-white/40 text-xs text-center",children:["Press ",s.jsx("kbd",{className:"px-1 py-0.5 bg-white/10 rounded text-white/60",children:"Ctrl+Shift+T"})," to toggle"]})})]})}),(0,s.jsxs)(o.motion.button,{onClick:()=>x(!p),className:"relative p-3 rounded-full overflow-hidden group",style:{background:t?"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)":"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:t?"0 8px 25px rgba(255, 215, 0, 0.4)":"0 8px 25px rgba(217, 70, 239, 0.4)"},whileHover:{scale:1.1},whileTap:{scale:.95},animate:{rotate:p?180:0},transition:{duration:.3},children:[s.jsx("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500",style:{background:"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)",backgroundSize:"200% 100%",animation:"shimmer 2s ease-in-out infinite"}}),s.jsx("div",{className:"relative z-10",children:p?s.jsx(h.Z,{className:"w-6 h-6 text-white"}):s.jsx(d.Z,{className:"w-6 h-6 text-white"})}),s.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white shadow-lg"})]}),s.jsx(o.AnimatePresence,{children:!p&&s.jsx(o.motion.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:-10},className:"absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 rounded-md text-xs text-white whitespace-nowrap pointer-events-none",style:{background:"rgba(0, 0, 0, 0.8)",backdropFilter:"blur(10px)"},children:t?"Gold Theme":"Purple Theme"})})]})};t()}catch(e){t(e)}})},7903:(e,a,r)=>{"use strict";r.d(a,{Z:()=>i});var t=r(997);r(6689);var s=r(1649);let i=({children:e,session:a})=>t.jsx(s.SessionProvider,{session:a,refetchInterval:300,refetchOnWindowFocus:!0,children:e})},4801:(e,a,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(a),r.d(a,{default:()=>$});var s=r(997),i=r(2600),o=r.n(i),n=r(9089),d=r.n(n),l=r(3598),c=r.n(l),m=r(8914),h=r.n(m),g=r(3679),p=r.n(g),x=r(1377),b=r(1162),u=r(6201),f=r(5632),y=r(6689),w=r(4696),v=r(7903),k=r(1388),j=r(1675),N=r(3807);r(7016);var F=e([u,N]);[u,N]=F.then?(await F)():F;let $=(0,x.appWithTranslation)(function({Component:e,pageProps:{session:a,...r}}){let t=(0,f.useRouter)(),{locale:i}=t,n="ar"===i;return(0,y.useEffect)(()=>{document.documentElement.dir=n?"rtl":"ltr",document.documentElement.lang=i||"ar",(0,k.G)()},[i,n]),s.jsx(w.Z,{children:s.jsx("div",{className:`${o().variable} ${d().variable} ${c().variable} ${h().variable} ${p().variable} ${n?"font-cairo":"font-sans"}`,children:s.jsx(v.Z,{session:a,children:s.jsx(j.f6,{defaultTheme:"gold",children:(0,s.jsxs)(b.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!1,children:[s.jsx(e,{...r}),s.jsx(N.Z,{}),s.jsx(u.Toaster,{position:n?"top-left":"top-right",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",direction:n?"rtl":"ltr"}}})]})})})})})});t()}catch(e){t(e)}})},1675:(e,a,r)=>{"use strict";r.d(a,{f6:()=>k,Fg:()=>j});var t=r(997),s=r(6689);let i={name:"gold",displayName:"Gold Premium",colors:{primary:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},secondary:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},accent:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(255, 215, 0, 0.12)",border:"rgba(255, 215, 0, 0.25)",shadow:"0 8px 32px rgba(255, 215, 0, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#FFD700",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:`
      radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),
      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)
    `,secondary:`
      radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
    `,tertiary:`
      radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)
    `},gradients:{primary:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",secondary:"linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)",accent:"linear-gradient(135deg, #DAA520 0%, #B8860B 100%)",text:`linear-gradient(90deg,
      #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%,
      #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%,
      #DAA520 80%, #B8860B 90%, #8B6914 100%)`,button:"linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%)",card:"linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(255, 215, 0, 0.1)",md:"0 4px 8px rgba(255, 215, 0, 0.15)",lg:"0 8px 16px rgba(255, 215, 0, 0.2)",xl:"0 12px 24px rgba(255, 215, 0, 0.25)",glass:"0 8px 32px rgba(255, 215, 0, 0.15)",premium:"0 25px 50px rgba(255, 215, 0, 0.3)"},animations:{shimmer:`
      @keyframes goldShimmer {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }
    `,glow:`
      @keyframes goldGlow {
        0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
        50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
      }
    `,pulse:`
      @keyframes goldPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `,float:`
      @keyframes goldFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
    `}},o={"--theme-primary-50":i.colors.primary[50],"--theme-primary-100":i.colors.primary[100],"--theme-primary-200":i.colors.primary[200],"--theme-primary-300":i.colors.primary[300],"--theme-primary-400":i.colors.primary[400],"--theme-primary-500":i.colors.primary[500],"--theme-primary-600":i.colors.primary[600],"--theme-primary-700":i.colors.primary[700],"--theme-primary-800":i.colors.primary[800],"--theme-primary-900":i.colors.primary[900],"--theme-glass-bg":i.colors.glass.background,"--theme-glass-border":i.colors.glass.border,"--theme-glass-shadow":i.colors.glass.shadow,"--theme-glass-blur":i.colors.glass.backdropBlur,"--theme-bg-primary":i.backgrounds.primary,"--theme-bg-secondary":i.backgrounds.secondary,"--theme-bg-tertiary":i.backgrounds.tertiary||i.backgrounds.secondary,"--theme-text-primary":i.colors.text.primary,"--theme-text-secondary":i.colors.text.secondary,"--theme-text-accent":i.colors.text.accent,"--theme-text-muted":i.colors.text.muted,"--theme-gradient-primary":i.gradients.primary,"--theme-gradient-secondary":i.gradients.secondary,"--theme-gradient-accent":i.gradients.accent,"--theme-gradient-text":i.gradients.text,"--theme-gradient-button":i.gradients.button,"--theme-gradient-card":i.gradients.card,"--theme-shadow-sm":i.shadows.sm,"--theme-shadow-md":i.shadows.md,"--theme-shadow-lg":i.shadows.lg,"--theme-shadow-xl":i.shadows.xl,"--theme-shadow-glass":i.shadows.glass,"--theme-shadow-premium":i.shadows.premium},n={name:"purple",displayName:"Purple Dark",colors:{primary:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},secondary:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},accent:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(217, 70, 239, 0.12)",border:"rgba(217, 70, 239, 0.25)",shadow:"0 8px 32px rgba(217, 70, 239, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#d946ef",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:`
      radial-gradient(circle at 50% 50%, rgba(217, 70, 239, 0.08) 0%, transparent 60%),
      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)
    `,secondary:`
      radial-gradient(ellipse at top, rgba(192, 38, 211, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
    `,tertiary:`
      radial-gradient(ellipse at bottom, rgba(162, 28, 175, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)
    `},gradients:{primary:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",secondary:"linear-gradient(135deg, #c026d3 0%, #86198f 100%)",accent:"linear-gradient(135deg, #e879f9 0%, #c026d3 100%)",text:`linear-gradient(90deg,
      #701a75 0%, #86198f 10%, #a21caf 20%, #c026d3 30%,
      #d946ef 40%, #e879f9 50%, #d946ef 60%, #c026d3 70%,
      #a21caf 80%, #86198f 90%, #701a75 100%)`,button:"linear-gradient(135deg, #d946ef 0%, #a21caf 50%, #86198f 100%)",card:"linear-gradient(135deg, rgba(217, 70, 239, 0.08) 0%, rgba(217, 70, 239, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(217, 70, 239, 0.1)",md:"0 4px 8px rgba(217, 70, 239, 0.15)",lg:"0 8px 16px rgba(217, 70, 239, 0.2)",xl:"0 12px 24px rgba(217, 70, 239, 0.25)",glass:"0 8px 32px rgba(217, 70, 239, 0.15)",premium:"0 25px 50px rgba(217, 70, 239, 0.3)"},animations:{shimmer:`
      @keyframes purpleShimmer {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }
    `,glow:`
      @keyframes purpleGlow {
        0%, 100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.3); }
        50% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.6); }
      }
    `,pulse:`
      @keyframes purplePulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `,float:`
      @keyframes purpleFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
    `}},d={"--theme-primary-50":n.colors.primary[50],"--theme-primary-100":n.colors.primary[100],"--theme-primary-200":n.colors.primary[200],"--theme-primary-300":n.colors.primary[300],"--theme-primary-400":n.colors.primary[400],"--theme-primary-500":n.colors.primary[500],"--theme-primary-600":n.colors.primary[600],"--theme-primary-700":n.colors.primary[700],"--theme-primary-800":n.colors.primary[800],"--theme-primary-900":n.colors.primary[900],"--theme-glass-bg":n.colors.glass.background,"--theme-glass-border":n.colors.glass.border,"--theme-glass-shadow":n.colors.glass.shadow,"--theme-glass-blur":n.colors.glass.backdropBlur,"--theme-bg-primary":n.backgrounds.primary,"--theme-bg-secondary":n.backgrounds.secondary,"--theme-bg-tertiary":n.backgrounds.tertiary||n.backgrounds.secondary,"--theme-text-primary":n.colors.text.primary,"--theme-text-secondary":n.colors.text.secondary,"--theme-text-accent":n.colors.text.accent,"--theme-text-muted":n.colors.text.muted,"--theme-gradient-primary":n.gradients.primary,"--theme-gradient-secondary":n.gradients.secondary,"--theme-gradient-accent":n.gradients.accent,"--theme-gradient-text":n.gradients.text,"--theme-gradient-button":n.gradients.button,"--theme-gradient-card":n.gradients.card,"--theme-shadow-sm":n.shadows.sm,"--theme-shadow-md":n.shadows.md,"--theme-shadow-lg":n.shadows.lg,"--theme-shadow-xl":n.shadows.xl,"--theme-shadow-glass":n.shadows.glass,"--theme-shadow-premium":n.shadows.premium},l={gold:i,purple:n},c={gold:o,purple:d},m=e=>l[e],h=e=>c[e],g=e=>{let a=h(e),r=document.documentElement;r.classList.remove("theme-gold","theme-purple"),r.classList.add(`theme-${e}`),Object.entries(a).forEach(([e,a])=>{r.style.setProperty(e,a)})},p=()=>null,x=e=>{},b=e=>`theme-${e}`,u=()=>{if("undefined"==typeof document)return;let e=document.createElement("style");e.textContent=`
    * {
      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
    }
  `,document.head.appendChild(e),setTimeout(()=>{document.head.removeChild(e)},300)},f=e=>{let a=m(e),r=h(e);return`
    .theme-${e} {
      ${Object.entries(r).map(([e,a])=>`${e}: ${a};`).join("\n      ")}
    }
    
    /* Theme-specific animations */
    ${a.animations.shimmer}
    ${a.animations.glow}
    ${a.animations.pulse}
    ${a.animations.float}
    
    /* Theme-specific utilities */
    .theme-${e} .bg-theme-primary {
      background: var(--theme-bg-primary);
    }
    
    .theme-${e} .bg-theme-secondary {
      background: var(--theme-bg-secondary);
    }
    
    .theme-${e} .glass-effect {
      background: var(--theme-glass-bg);
      border: 1px solid var(--theme-glass-border);
      box-shadow: var(--theme-glass-shadow);
      backdrop-filter: var(--theme-glass-blur);
      -webkit-backdrop-filter: var(--theme-glass-blur);
    }
    
    .theme-${e} .text-theme-gradient {
      background: var(--theme-gradient-text);
      background-size: 300% 100%;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: ${e}Shimmer 4s ease-in-out infinite;
    }
    
    .theme-${e} .btn-theme-primary {
      background: var(--theme-gradient-button);
      box-shadow: var(--theme-shadow-premium);
      color: white;
      border: 1px solid var(--theme-glass-border);
    }
    
    .theme-${e} .btn-theme-primary:hover {
      animation: ${e}Glow 2s ease-in-out infinite;
    }
  `},y=e=>{if("undefined"==typeof document)return;let a=document.getElementById(`theme-${e}-styles`);a&&a.remove();let r=document.createElement("style");r.id=`theme-${e}-styles`,r.textContent=f(e),document.head.appendChild(r)},w=(e="gold")=>{let a=p(),r=a||e;return g(r),y(r),r},v=(0,s.createContext)(void 0),k=({children:e,defaultTheme:a="gold"})=>{let[r,i]=(0,s.useState)(a),[o,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=w(a);i(e),n(!0)},[a]);let d=m(r),l=(0,s.useCallback)(e=>{e!==r&&(u(),g(e),y(e),i(e),x(e))},[r]);(0,s.useEffect)(()=>{},[r,l]);let c={currentTheme:d,themeName:r,switchTheme:l,isGoldTheme:"gold"===r,isPurpleTheme:"purple"===r,themeClasses:b(r)};return o?t.jsx(v.Provider,{value:c,children:t.jsx("div",{className:`theme-provider ${b(r)}`,children:e})}):null},j=()=>{let e=(0,s.useContext)(v);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},1388:(e,a,r)=>{"use strict";function t(){}r.d(a,{G:()=>t}),console.error,console.warn},7016:()=>{}};