"use strict";exports.id=952,exports.ids=[952],exports.modules={6021:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},4591:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},8025:(e,t,n)=>{n.d(t,{V:()=>ej});var r,o,l,a,i,u,s,c,d,f,m,p,v,h=n(6689),g=n.t(h,2),E=n(8314),w=n(5559),b=n(1742);function y(e,t,n,r){let o=(0,b.E)(n);(0,h.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}var T=n(1061),L=n(1554);function k(e){let t=(0,w.z)(e),n=(0,h.useRef)(!1);(0,h.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,L.Y)(()=>{n.current&&t()})}),[t])}var P=n(4727);function S(e){return P.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}function O(...e){return(0,h.useMemo)(()=>S(...e),[...e])}var C=n(6460),A=n(3401);function M(e,t,n){let r=(0,b.E)(t);(0,h.useEffect)(()=>{function t(e){r.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}var x=((r=x||{})[r.Forwards=0]="Forwards",r[r.Backwards=1]="Backwards",r);function F(e,t){let n=(0,h.useRef)([]),r=(0,w.z)(e);(0,h.useEffect)(()=>{let e=[...n.current];for(let[o,l]of t.entries())if(n.current[o]!==l){let o=r(t,e);return n.current=t,o}},[r,...t])}var R=n(544),N=((o=N||{})[o.None=1]="None",o[o.Focusable=2]="Focusable",o[o.Hidden=4]="Hidden",o);let D=(0,R.yV)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,R.sY)({ourProps:l,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),j=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&j[0]!==e.target&&(j.unshift(e.target),(j=j.filter(e=>null!=e&&e.isConnected)).splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var H=n(3703);let Z=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var I=((l=I||{})[l.First=1]="First",l[l.Previous=2]="Previous",l[l.Next=4]="Next",l[l.Last=8]="Last",l[l.WrapAround=16]="WrapAround",l[l.NoScroll=32]="NoScroll",l),V=((a=V||{})[a.Error=0]="Error",a[a.Overflow=1]="Overflow",a[a.Success=2]="Success",a[a.Underflow=3]="Underflow",a),B=((i=B||{})[i.Previous=-1]="Previous",i[i.Next=1]="Next",i),Y=((u=Y||{})[u.Strict=0]="Strict",u[u.Loose=1]="Loose",u),W=((s=W||{})[s.Keyboard=0]="Keyboard",s[s.Mouse=1]="Mouse",s);function z(e){null==e||e.focus({preventScroll:!0})}function U(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var l,a,i;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Z)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.includes(e))),r=null!=r?r:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,v;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(v=s[e])||v.focus(f),m+=c}while(v!==u.activeElement);return 6&t&&null!=(i=null==(a=null==(l=v)?void 0:l.matches)?void 0:a.call(l,"textarea,input"))&&i&&v.select(),2}function _(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var G=((c=G||{})[c.None=1]="None",c[c.InitialFocus=2]="InitialFocus",c[c.TabLock=4]="TabLock",c[c.FocusLock=8]="FocusLock",c[c.RestoreFocus=16]="RestoreFocus",c[c.All=30]="All",c);let $=Object.assign((0,R.yV)(function(e,t){let n,r=(0,h.useRef)(null),o=(0,A.T)(r,t),{initialFocus:l,containers:a,features:i=30,...u}=e;(0,C.H)()||(i=1);let s=O(r);(function({ownerDocument:e},t){let n=function(e=!0){let t=(0,h.useRef)(j.slice());return F(([e],[n])=>{!0===n&&!1===e&&(0,L.Y)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=j.slice())},[e,j,t]),(0,w.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);F(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&z(n())},[t]),k(()=>{t&&z(n())})})({ownerDocument:s},!!(16&i));let c=function({ownerDocument:e,container:t,initialFocus:n},r){let o=(0,h.useRef)(null),l=(0,T.t)();return F(()=>{if(!r)return;let a=t.current;a&&(0,L.Y)(()=>{if(!l.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t){o.current=t;return}}else if(a.contains(t)){o.current=t;return}null!=n&&n.current?z(n.current):U(a,I.First)===V.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[r]),o}({ownerDocument:s,container:r,initialFocus:l},!!(2&i));(function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let l=(0,T.t)();y(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!l.current)return;let a=_(n);t.current instanceof HTMLElement&&a.add(t.current);let i=r.current;if(!i)return;let u=e.target;u&&u instanceof HTMLElement?q(a,u)?(r.current=u,z(u)):(e.preventDefault(),e.stopPropagation(),z(i)):z(r.current)},!0)})({ownerDocument:s,container:r,containers:a,previousActiveElement:c},!!(8&i));let d=(n=(0,h.useRef)(0),M("keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),f=(0,w.z)(e=>{let t=r.current;t&&(0,H.E)(d.current,{[x.Forwards]:()=>{U(t,I.First,{skipElements:[e.relatedTarget]})},[x.Backwards]:()=>{U(t,I.Last,{skipElements:[e.relatedTarget]})}})}),m=(0,E.G)(),p=(0,h.useRef)(!1);return h.createElement(h.Fragment,null,!!(4&i)&&h.createElement(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:N.Focusable}),(0,R.sY)({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(p.current=!0,m.requestAnimationFrame(()=>{p.current=!1}))},onBlur(e){let t=_(a);r.current instanceof HTMLElement&&t.add(r.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(q(t,n)||(p.current?U(r.current,(0,H.E)(d.current,{[x.Forwards]:()=>I.Next,[x.Backwards]:()=>I.Previous})|I.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&z(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),!!(4&i)&&h.createElement(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:N.Focusable}))}),{features:G});function q(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var K=n(6405),J=n(8064);let X=(0,h.createContext)(!1);function Q(e){return h.createElement(X.Provider,{value:e.force},e.children)}let ee=h.Fragment,et=h.Fragment,en=(0,h.createContext)(null),er=(0,h.createContext)(null),eo=Object.assign((0,R.yV)(function(e,t){let n=(0,h.useRef)(null),r=(0,A.T)((0,A.h)(e=>{n.current=e}),t),o=O(n),l=function(e){let t=(0,h.useContext)(X),n=(0,h.useContext)(en),r=O(e),[o,l]=(0,h.useState)(()=>{if(!t&&null!==n||P.O.isServer)return null;let e=null==r?void 0:r.getElementById("headlessui-portal-root");if(e)return e;if(null===r)return null;let o=r.createElement("div");return o.setAttribute("id","headlessui-portal-root"),r.body.appendChild(o)});return(0,h.useEffect)(()=>{null!==o&&(null!=r&&r.body.contains(o)||null==r||r.body.appendChild(o))},[o,r]),(0,h.useEffect)(()=>{t||null!==n&&l(n.current)},[n,l,t]),o}(n),[a]=(0,h.useState)(()=>{var e;return P.O.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),i=(0,h.useContext)(er),u=(0,C.H)();return(0,J.e)(()=>{!l||!a||l.contains(a)||(a.setAttribute("data-headlessui-portal",""),l.appendChild(a))},[l,a]),(0,J.e)(()=>{if(a&&i)return i.register(a)},[i,a]),k(()=>{var e;l&&a&&(a instanceof Node&&l.contains(a)&&l.removeChild(a),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))}),u&&l&&a?(0,K.createPortal)((0,R.sY)({ourProps:{ref:r},theirProps:e,defaultTag:ee,name:"Portal"}),a):null}),{Group:(0,R.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,A.T)(t)};return h.createElement(en.Provider,{value:n},(0,R.sY)({ourProps:o,theirProps:r,defaultTag:et,name:"Popover.Group"}))})}),el="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:ea,useEffect:ei,useLayoutEffect:eu,useDebugValue:es}=g;function ec(e){let t=e.getSnapshot,n=e.value;try{let e=t();return!el(n,e)}catch{return!0}}let ed="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,ef=ed?function(e,t,n){let r=t(),[{inst:o},l]=ea({inst:{value:r,getSnapshot:t}});return eu(()=>{o.value=r,o.getSnapshot=t,ec(o)&&l({inst:o})},[e,r,t]),ei(()=>(ec(o)&&l({inst:o}),e(()=>{ec(o)&&l({inst:o})})),[e]),es(r),r}:function(e,t,n){return t()},em="useSyncExternalStore"in g?g.useSyncExternalStore:ef;var ep=n(6173);function ev(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}let eh=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let l=t[e].call(n,...o);l&&(n=l,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,ep.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},l=[ev()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,ep.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),a=e.querySelector(o);a&&!r(a)&&(l=a)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth},after({doc:e,d:t}){let n=e.documentElement,o=n.clientWidth-n.offsetWidth,l=r-o;t.style(n,"paddingRight",`${l}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];l.forEach(({before:e})=>null==e?void 0:e(o)),l.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});eh.subscribe(()=>{let e=eh.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&eh.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&eh.dispatch("TEARDOWN",n)}});let eg=null!=(v=h.useId)?v:function(){let e=(0,C.H)(),[t,n]=h.useState(e?()=>P.O.nextId():null);return(0,J.e)(()=>{null===t&&n(P.O.nextId())},[t]),null!=t?""+t:void 0},eE=new Map,ew=new Map;function eb(e,t=!0){(0,J.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=ew.get(r))?n:0;return ew.set(r,o+1),0!==o||(eE.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=ew.get(r))?e:1;if(1===t?ew.delete(r):ew.set(r,t-1),1!==t)return;let n=eE.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,eE.delete(r))}},[e,t])}function ey(e,t,n){let r=(0,b.E)(t);(0,h.useEffect)(()=>{function t(e){r.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var eT=n(7215);let eL=(0,h.createContext)(()=>{});eL.displayName="StackContext";var ek=((d=ek||{})[d.Add=0]="Add",d[d.Remove=1]="Remove",d);function eP({children:e,onUpdate:t,type:n,element:r,enabled:o}){let l=(0,h.useContext)(eL),a=(0,w.z)((...e)=>{null==t||t(...e),l(...e)});return(0,J.e)(()=>{let e=void 0===o||!0===o;return e&&a(0,n,r),()=>{e&&a(1,n,r)}},[a,n,r,o]),h.createElement(eL.Provider,{value:a},e)}let eS=(0,h.createContext)(null),eO=Object.assign((0,R.yV)(function(e,t){let n=eg(),{id:r=`headlessui-description-${n}`,...o}=e,l=function e(){let t=(0,h.useContext)(eS);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=(0,A.T)(t);(0,J.e)(()=>l.register(r),[r,l.register]);let i={ref:a,...l.props,id:r};return(0,R.sY)({ourProps:i,theirProps:o,slot:l.slot||{},defaultTag:"p",name:l.name||"Description"})}),{});var eC=((f=eC||{}).Space=" ",f.Enter="Enter",f.Escape="Escape",f.Backspace="Backspace",f.Delete="Delete",f.ArrowLeft="ArrowLeft",f.ArrowUp="ArrowUp",f.ArrowRight="ArrowRight",f.ArrowDown="ArrowDown",f.Home="Home",f.End="End",f.PageUp="PageUp",f.PageDown="PageDown",f.Tab="Tab",f),eA=((m=eA||{})[m.Open=0]="Open",m[m.Closed=1]="Closed",m),eM=((p=eM||{})[p.SetTitleId=0]="SetTitleId",p);let ex={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eF=(0,h.createContext)(null);function eR(e){let t=(0,h.useContext)(eF);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eR),t}return t}function eN(e,t){return(0,H.E)(t.type,ex,e,t)}eF.displayName="DialogContext";let eD=R.AN.RenderStrategy|R.AN.Static,ej=Object.assign((0,R.yV)(function(e,t){let n,r,o,l,a,i=eg(),{id:u=`headlessui-dialog-${i}`,open:s,onClose:c,initialFocus:d,role:f="dialog",__demoMode:m=!1,...p}=e,[v,g]=(0,h.useState)(0),E=(0,h.useRef)(!1);f="dialog"===f||"alertdialog"===f?f:(E.current||(E.current=!0,console.warn(`Invalid role [${f}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let b=(0,eT.oJ)();void 0===s&&null!==b&&(s=(b&eT.ZM.Open)===eT.ZM.Open);let T=(0,h.useRef)(null),L=(0,A.T)(T,t),k=O(T),P=e.hasOwnProperty("open")||null!==b,x=e.hasOwnProperty("onClose");if(!P&&!x)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!P)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!x)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof s)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s}`);if("function"!=typeof c)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${c}`);let F=s?0:1,[j,I]=(0,h.useReducer)(eN,{titleId:null,descriptionId:null,panelRef:(0,h.createRef)()}),V=(0,w.z)(()=>c(!1)),B=(0,w.z)(e=>I({type:0,id:e})),W=!!(0,C.H)()&&!m&&0===F,z=v>1,U=null!==(0,h.useContext)(eF),[_,G]=(n=(0,h.useContext)(er),r=(0,h.useRef)([]),o=(0,w.z)(e=>(r.current.push(e),n&&n.register(e),()=>l(e))),l=(0,w.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),a=(0,h.useMemo)(()=>({register:o,unregister:l,portals:r}),[o,l,r]),[r,(0,h.useMemo)(()=>function({children:e}){return h.createElement(er.Provider,{value:a},e)},[a])]),{resolveContainers:q,mainTreeNodeRef:K,MainTreeNode:X}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=(0,h.useRef)(null!=(r=null==n?void 0:n.current)?r:null),l=O(o),a=(0,w.z)(()=>{var n,r,a;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(n=null==l?void 0:l.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(a=null==(r=o.current)?void 0:r.getRootNode())?void 0:a.host)||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:a,contains:(0,w.z)(e=>a().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,h.useMemo)(()=>function(){return null!=n?null:h.createElement(D,{features:N.Hidden,ref:o})},[o,n])}}({portals:_,defaultContainers:[{get current(){var ee;return null!=(ee=j.panelRef.current)?ee:T.current}}]}),et=null!==b&&(b&eT.ZM.Closing)===eT.ZM.Closing,en=!U&&!et&&W;eb((0,h.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==k?void 0:k.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),en);let el=!!z||W;eb((0,h.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==k?void 0:k.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),el),function(e,t,n=!0){let r=(0,h.useRef)(!1);function o(n,o){if(!r.current||n.defaultPrevented)return;let l=o(n);if(null!==l&&l.getRootNode().contains(l)&&l.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(l)||n.composed&&n.composedPath().includes(e))return}return function(e,t=0){var n;return e!==(null==(n=S(e))?void 0:n.body)&&(0,H.E)(t,{0:()=>e.matches(Z),1(){let t=e;for(;null!==t;){if(t.matches(Z))return!0;t=t.parentElement}return!1}})}(l,Y.Loose)||-1===l.tabIndex||n.preventDefault(),t(n,l)}}(0,h.useEffect)(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);let l=(0,h.useRef)(null);ey("pointerdown",e=>{var t,n;r.current&&(l.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ey("mousedown",e=>{var t,n;r.current&&(l.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ey("click",e=>{ev()||/Android/gi.test(window.navigator.userAgent)||l.current&&(o(e,()=>l.current),l.current=null)},!0),ey("touchend",e=>o(e,()=>e.target instanceof HTMLElement?e.target:null),!0),M("blur",e=>o(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}(q,e=>{e.preventDefault(),V()},!(!W||z));let ea=!(z||0!==F);y(null==k?void 0:k.defaultView,"keydown",e=>{ea&&(e.defaultPrevented||e.key===eC.Escape&&(e.preventDefault(),e.stopPropagation(),V()))}),function(e,t,n=()=>[document.body]){var r;let o,l;r=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}},o=em(eh.subscribe,eh.getSnapshot,eh.getSnapshot),(l=e?o.get(e):void 0)&&l.count,(0,J.e)(()=>{if(!(!e||!t))return eh.dispatch("PUSH",e,r),()=>eh.dispatch("POP",e,r)},[t,e])}(k,!(et||0!==F||U),q),(0,h.useEffect)(()=>{if(0!==F||!T.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&V()}});return e.observe(T.current),()=>e.disconnect()},[F,T,V]);let[ei,eu]=function(){let[e,t]=(0,h.useState)([]);return[e.length>0?e.join(" "):void 0,(0,h.useMemo)(()=>function(e){let n=(0,w.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,h.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return h.createElement(eS.Provider,{value:r},e.children)},[t])]}(),es=(0,h.useMemo)(()=>[{dialogState:F,close:V,setTitleId:B},j],[F,j,V,B]),ec=(0,h.useMemo)(()=>({open:0===F}),[F]),ed={ref:L,id:u,role:f,"aria-modal":0===F||void 0,"aria-labelledby":j.titleId,"aria-describedby":ei};return h.createElement(eP,{type:"Dialog",enabled:0===F,element:T,onUpdate:(0,w.z)((e,t)=>{"Dialog"===t&&(0,H.E)(e,{[ek.Add]:()=>g(e=>e+1),[ek.Remove]:()=>g(e=>e-1)})})},h.createElement(Q,{force:!0},h.createElement(eo,null,h.createElement(eF.Provider,{value:es},h.createElement(eo.Group,{target:T},h.createElement(Q,{force:!1},h.createElement(eu,{slot:ec,name:"Dialog.Description"},h.createElement($,{initialFocus:d,containers:q,features:W?(0,H.E)(z?"parent":"leaf",{parent:$.features.RestoreFocus,leaf:$.features.All&~$.features.FocusLock}):$.features.None},h.createElement(G,null,(0,R.sY)({ourProps:ed,theirProps:p,slot:ec,defaultTag:"div",features:eD,visible:0===F,name:"Dialog"}))))))))),h.createElement(X,null))}),{Backdrop:(0,R.yV)(function(e,t){let n=eg(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:l},a]=eR("Dialog.Backdrop"),i=(0,A.T)(t);(0,h.useEffect)(()=>{if(null===a.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[a.panelRef]);let u=(0,h.useMemo)(()=>({open:0===l}),[l]);return h.createElement(Q,{force:!0},h.createElement(eo,null,(0,R.sY)({ourProps:{ref:i,id:r,"aria-hidden":!0},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,R.yV)(function(e,t){let n=eg(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:l},a]=eR("Dialog.Panel"),i=(0,A.T)(t,a.panelRef),u=(0,h.useMemo)(()=>({open:0===l}),[l]),s=(0,w.z)(e=>{e.stopPropagation()});return(0,R.sY)({ourProps:{ref:i,id:r,onClick:s},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,R.yV)(function(e,t){let n=eg(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:l,close:a}]=eR("Dialog.Overlay"),i=(0,A.T)(t),u=(0,w.z)(e=>{if(e.target===e.currentTarget){if(function(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),a()}}),s=(0,h.useMemo)(()=>({open:0===l}),[l]);return(0,R.sY)({ourProps:{ref:i,id:r,"aria-hidden":!0,onClick:u},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,R.yV)(function(e,t){let n=eg(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:l,setTitleId:a}]=eR("Dialog.Title"),i=(0,A.T)(t);(0,h.useEffect)(()=>(a(r),()=>a(null)),[r,a]);let u=(0,h.useMemo)(()=>({open:0===l}),[l]);return(0,R.sY)({ourProps:{ref:i,id:r},theirProps:o,slot:u,defaultTag:"h2",name:"Dialog.Title"})}),Description:eO})},9442:(e,t,n)=>{n.d(t,{u:()=>F});var r,o=n(6689),l=n(8314),a=n(5559),i=n(1061),u=n(8064),s=n(1742),c=n(6460),d=n(3401),f=n(6173),m=n(3703);function p(e,...t){e&&t.length>0&&e.classList.add(...t)}function v(e,...t){e&&t.length>0&&e.classList.remove(...t)}var h=n(7215),g=n(4020),E=n(544);function w(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let b=(0,o.createContext)(null);b.displayName="TransitionContext";var y=((r=y||{}).Visible="visible",r.Hidden="hidden",r);let T=(0,o.createContext)(null);function L(e){return"children"in e?L(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function k(e,t){let n=(0,s.E)(e),r=(0,o.useRef)([]),u=(0,i.t)(),c=(0,l.G)(),d=(0,a.z)((e,t=E.l4.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,m.E)(t,{[E.l4.Unmount](){r.current.splice(o,1)},[E.l4.Hidden](){r.current[o].state="hidden"}}),c.microTask(()=>{var e;!L(r)&&u.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,a.z)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,E.l4.Unmount)}),p=(0,o.useRef)([]),v=(0,o.useRef)(Promise.resolve()),h=(0,o.useRef)({enter:[],leave:[],idle:[]}),g=(0,a.z)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(h.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?v.current=v.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),w=(0,a.z)((e,t,n)=>{Promise.all(h.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:g,onStop:w,wait:v,chains:h}),[f,d,r,g,w,h,v])}function P(){}T.displayName="NestingContext";let S=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function O(e){var t;let n={};for(let r of S)n[r]=null!=(t=e[r])?t:P;return n}let C=E.AN.RenderStrategy,A=(0,E.yV)(function(e,t){let{show:n,appear:r=!1,unmount:l=!0,...i}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let m=(0,h.oJ)();if(void 0===n&&null!==m&&(n=(m&h.ZM.Open)===h.ZM.Open),![!0,!1].includes(n))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,v]=(0,o.useState)(n?"visible":"hidden"),g=k(()=>{v("hidden")}),[w,y]=(0,o.useState)(!0),P=(0,o.useRef)([n]);(0,u.e)(()=>{!1!==w&&P.current[P.current.length-1]!==n&&(P.current.push(n),y(!1))},[P,n]);let S=(0,o.useMemo)(()=>({show:n,appear:r,initial:w}),[n,r,w]);(0,o.useEffect)(()=>{if(n)v("visible");else if(L(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&v("hidden")}else v("hidden")},[n,g]);let O={unmount:l},A=(0,a.z)(()=>{var t;w&&y(!1),null==(t=e.beforeEnter)||t.call(e)}),x=(0,a.z)(()=>{var t;w&&y(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(T.Provider,{value:g},o.createElement(b.Provider,{value:S},(0,E.sY)({ourProps:{...O,as:o.Fragment,children:o.createElement(M,{ref:f,...O,...i,beforeEnter:A,beforeLeave:x})},theirProps:{},defaultTag:o.Fragment,features:C,visible:"visible"===p,name:"Transition"})))}),M=(0,E.yV)(function(e,t){var n,r,y;let P;let{beforeEnter:S,afterEnter:A,beforeLeave:M,afterLeave:x,enter:F,enterFrom:R,enterTo:N,entered:D,leave:j,leaveFrom:H,leaveTo:Z,...I}=e,V=(0,o.useRef)(null),B=(0,d.T)(V,t),Y=null==(n=I.unmount)||n?E.l4.Unmount:E.l4.Hidden,{show:W,appear:z,initial:U}=function(){let e=(0,o.useContext)(b);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[_,G]=(0,o.useState)(W?"visible":"hidden"),$=function(){let e=(0,o.useContext)(T);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:q,unregister:K}=$;(0,o.useEffect)(()=>q(V),[q,V]),(0,o.useEffect)(()=>{if(Y===E.l4.Hidden&&V.current){if(W&&"visible"!==_){G("visible");return}return(0,m.E)(_,{hidden:()=>K(V),visible:()=>q(V)})}},[_,V,q,K,W,Y]);let J=(0,s.E)({base:w(I.className),enter:w(F),enterFrom:w(R),enterTo:w(N),entered:w(D),leave:w(j),leaveFrom:w(H),leaveTo:w(Z)}),X=(y={beforeEnter:S,afterEnter:A,beforeLeave:M,afterLeave:x},P=(0,o.useRef)(O(y)),(0,o.useEffect)(()=>{P.current=O(y)},[y]),P),Q=(0,c.H)();(0,o.useEffect)(()=>{if(Q&&"visible"===_&&null===V.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[V,_,Q]);let ee=z&&W&&U,et=Q&&(!U||z)?W?"enter":"leave":"idle",en=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,i.t)(),l=(0,o.useCallback)(e=>{r.current&&n(t=>t|e)},[t,r]),a=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:l,hasFlag:a,removeFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t&~e)},[n,r]),toggleFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t^e)},[n])}}(0),er=(0,a.z)(e=>(0,m.E)(e,{enter:()=>{en.addFlag(h.ZM.Opening),X.current.beforeEnter()},leave:()=>{en.addFlag(h.ZM.Closing),X.current.beforeLeave()},idle:()=>{}})),eo=(0,a.z)(e=>(0,m.E)(e,{enter:()=>{en.removeFlag(h.ZM.Opening),X.current.afterEnter()},leave:()=>{en.removeFlag(h.ZM.Closing),X.current.afterLeave()},idle:()=>{}})),el=k(()=>{G("hidden"),K(V)},$),ea=(0,o.useRef)(!1);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:a}){let c=(0,i.t)(),d=(0,l.G)(),h=(0,s.E)(n);(0,u.e)(()=>{e&&(h.current="enter")},[e]),(0,u.e)(()=>{let e=(0,f.k)();d.add(e.dispose);let n=t.current;if(n&&"idle"!==h.current&&c.current){var l,i,u;let t,s,c,d,g,E,w;return e.dispose(),o.current(h.current),e.add((l=r.current,i="enter"===h.current,u=()=>{e.dispose(),a.current(h.current)},s=i?"enter":"leave",c=(0,f.k)(),d=void 0!==u?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,u(...e)}):()=>{},"enter"===s&&(n.removeAttribute("hidden"),n.style.display=""),g=(0,m.E)(s,{enter:()=>l.enter,leave:()=>l.leave}),E=(0,m.E)(s,{enter:()=>l.enterTo,leave:()=>l.leaveTo}),w=(0,m.E)(s,{enter:()=>l.enterFrom,leave:()=>l.leaveFrom}),v(n,...l.base,...l.enter,...l.enterTo,...l.enterFrom,...l.leave,...l.leaveFrom,...l.leaveTo,...l.entered),p(n,...l.base,...g,...w),c.nextFrame(()=>{v(n,...l.base,...g,...w),p(n,...l.base,...g,...E),function(e,t){let n=(0,f.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,a]=[r,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),i=l+a;if(0!==i){n.group(n=>{n.setTimeout(()=>{t(),n.dispose()},i),n.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&n.dispose()})});let r=n.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),r())})}else t();n.add(()=>t()),n.dispose}(n,()=>(v(n,...l.base,...g),p(n,...l.base,...l.entered),d()))}),c.dispose)),e.dispose}},[n])}({immediate:ee,container:V,classes:J,direction:et,onStart:(0,s.E)(e=>{ea.current=!0,el.onStart(V,e,er)}),onStop:(0,s.E)(e=>{ea.current=!1,el.onStop(V,e,eo),"leave"!==e||L(el)||(G("hidden"),K(V))})});let ei=I;return ee?ei={...ei,className:(0,g.A)(I.className,...J.current.enter,...J.current.enterFrom)}:ea.current&&(ei.className=(0,g.A)(I.className,null==(r=V.current)?void 0:r.className),""===ei.className&&delete ei.className),o.createElement(T.Provider,{value:el},o.createElement(h.up,{value:(0,m.E)(_,{visible:h.ZM.Open,hidden:h.ZM.Closed})|en.flags},(0,E.sY)({ourProps:{ref:B},theirProps:ei,defaultTag:"div",features:C,visible:"visible"===_,name:"Transition.Child"})))}),x=(0,E.yV)(function(e,t){let n=null!==(0,o.useContext)(b),r=null!==(0,h.oJ)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(A,{ref:t,...e}):o.createElement(M,{ref:t,...e}))}),F=Object.assign(A,{Child:x,Root:A})},8314:(e,t,n)=>{n.d(t,{G:()=>l});var r=n(6689),o=n(6173);function l(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},5559:(e,t,n)=>{n.d(t,{z:()=>l});var r=n(6689),o=n(1742);let l=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},1061:(e,t,n)=>{n.d(t,{t:()=>l});var r=n(6689),o=n(8064);function l(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},8064:(e,t,n)=>{n.d(t,{e:()=>l});var r=n(6689),o=n(4727);let l=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},1742:(e,t,n)=>{n.d(t,{E:()=>l});var r=n(6689),o=n(8064);function l(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},6460:(e,t,n)=>{n.d(t,{H:()=>a});var r,o=n(6689),l=n(4727);function a(){let e;let t=(e="undefined"==typeof document,"useSyncExternalStore"in(r||(r=n.t(o,2)))&&(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[a,i]=o.useState(l.O.isHandoffComplete);return a&&!1===l.O.isHandoffComplete&&i(!1),o.useEffect(()=>{!0!==a&&i(!0)},[a]),o.useEffect(()=>l.O.handoff(),[]),!t&&a}},3401:(e,t,n)=>{n.d(t,{T:()=>i,h:()=>a});var r=n(6689),o=n(5559);let l=Symbol();function a(e,t=!0){return Object.assign(e,{[l]:t})}function i(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[l]))?void 0:n}},7215:(e,t,n)=>{n.d(t,{ZM:()=>a,oJ:()=>i,up:()=>u});var r,o=n(6689);let l=(0,o.createContext)(null);l.displayName="OpenClosedContext";var a=((r=a||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function i(){return(0,o.useContext)(l)}function u({value:e,children:t}){return o.createElement(l.Provider,{value:e},t)}},4020:(e,t,n)=>{n.d(t,{A:()=>r});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},6173:(e,t,n)=>{n.d(t,{k:()=>function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(1554)},4727:(e,t,n)=>{n.d(t,{O:()=>i});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class a{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let i=new a},3703:(e,t,n)=>{n.d(t,{E:()=>r});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}},1554:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},544:(e,t,n)=>{n.d(t,{AN:()=>u,l4:()=>s,sY:()=>c,yV:()=>p});var r,o,l=n(6689),a=n(4020),i=n(3703),u=((r=u||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:a,mergeRefs:u}){u=null!=u?u:f;let s=m(t,e);if(l)return d(s,n,r,a,u);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,a,u)}if(1&c){let{unmount:e=!0,...t}=s;return(0,i.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,a,u)})}return d(s,n,r,a,u)}function d(e,t={},n,r,o){let{as:i=n,children:u,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(p["data-headlessui-state"]=n.join(" "))}if(i===l.Fragment&&Object.keys(v(c)).length>0){if(!(0,l.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>(0,a.A)(null==e?void 0:e.className(...t),c.className):(0,a.A)(null==e?void 0:e.className,c.className);return(0,l.cloneElement)(f,Object.assign({},m(f.props,v(h(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,l.createElement)(i,Object.assign({},h(c,["ref"]),i!==l.Fragment&&d,i!==l.Fragment&&p),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function m(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function p(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},2972:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}),l=o},949:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))}),l=o},7013:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))}),l=o},5126:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),l=o},7330:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),l=o},6131:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),l=o},5342:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),l=o},3496:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}),l=o},666:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))}),l=o},7767:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}),l=o},924:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}),l=o},5374:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),l=o}};