"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[681],{6809:function(e,a,t){var r=t(2322),s=t(2784),o=t(8508),n=t(5632),i=t(1675),l=t(3496),c=t(5342),d=t(6131),u=t(5126);let m=(0,s.forwardRef)((e,a)=>{var t,m;let{label:g,placeholder:h,type:p="text",value:x,onChange:b,onBlur:E,error:f,touched:N=!1,required:v=!1,disabled:w=!1,autoComplete:S,leftIcon:y,rightIcon:I,onRightIconClick:j,className:T="",showPasswordStrength:A=!1,passwordStrength:_}=e,[R,C]=(0,s.useState)(!1),[D,O]=(0,s.useState)(!1),{currentTheme:P,themeName:F}=(0,i.Fg)(),L=(0,n.useRouter)(),{locale:M}=L,k="ar"===M,z="gold"===F,U=N&&f,V=x.length>0,Z="password"===p,B=Z&&R?"text":p,G="\n    absolute transition-all duration-300 pointer-events-none\n    ".concat(k?"right-4 font-tajawal":"left-4 font-sans","\n    ").concat(V||D?"top-2 text-xs ".concat(k?"right-3":"left-3"," px-2 rounded-md"):"top-4 text-base","\n    ").concat(U?"text-red-400":D?P.colors.text.accent:P.colors.text.secondary,"\n  "),Y="\n    w-full h-14 px-4 pt-6 pb-2 rounded-xl transition-all duration-300\n    ".concat(k?"text-right font-tajawal pr-4":"text-left font-sans pl-4","\n    ").concat(y?k?"pr-12":"pl-12":"","\n    ").concat(I||Z?k?"pl-12":"pr-12":"","\n    text-white placeholder-transparent\n    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ").concat(U?"border-2 border-red-400 focus:ring-red-400":"border border-white/20 focus:ring-2 ".concat(z?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50"),"\n  "),W=V||D?{background:z?"linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1))":"linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(126, 34, 206, 0.1))",backdropFilter:"blur(10px)",WebkitBackdropFilter:"blur(10px)"}:{};return(0,r.jsxs)(o.E.div,{className:"\n    relative w-full mb-6 group\n    ".concat(T,"\n  "),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsxs)("div",{className:"relative",children:[y&&(0,r.jsx)("div",{className:"absolute top-1/2 transform -translate-y-1/2 ".concat(k?"right-4":"left-4"," text-white/60 z-10"),children:y}),(0,r.jsx)("input",{ref:a,type:B,value:x,onChange:e=>b(e.target.value),onFocus:()=>O(!0),onBlur:()=>{O(!1),E&&E()},placeholder:h,required:v,disabled:w,autoComplete:S,className:Y,style:{background:U?"rgba(239, 68, 68, 0.1)":"rgba(255, 255, 255, 0.08)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},dir:k?"rtl":"ltr"}),(0,r.jsxs)("label",{className:G,style:W,children:[g,v&&(0,r.jsx)("span",{className:"text-red-400 ml-1",children:"*"})]}),(0,r.jsx)("div",{className:"absolute top-1/2 transform -translate-y-1/2 ".concat(k?"left-4":"right-4"," z-10"),children:Z?(0,r.jsx)("button",{type:"button",onClick:()=>C(!R),className:"text-white/60 hover:text-white transition-colors duration-200 p-1",tabIndex:-1,children:R?(0,r.jsx)(l.Z,{className:"w-5 h-5"}):(0,r.jsx)(c.Z,{className:"w-5 h-5"})}):I?(0,r.jsx)("button",{type:"button",onClick:j,className:"text-white/60 hover:text-white transition-colors duration-200 p-1",tabIndex:-1,children:I}):null}),N&&(0,r.jsx)("div",{className:"absolute top-1/2 transform -translate-y-1/2 ".concat(k?"left-12":"right-12"," z-10"),children:U?(0,r.jsx)(d.Z,{className:"w-5 h-5 text-red-400"}):x&&!U?(0,r.jsx)(u.Z,{className:"w-5 h-5 text-green-400"}):null})]}),A&&_&&x&&(0,r.jsx)(o.E.div,{className:"mt-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("div",{className:"flex-1 h-1 bg-white/20 rounded-full overflow-hidden",children:(0,r.jsx)("div",{className:"h-full transition-all duration-300 ".concat((t=_.score)<2?"bg-red-500":t<3?"bg-yellow-500":t<4?"bg-blue-500":"bg-green-500"),style:{width:(m=_.score,"".concat(m/4*100,"%"))}})}),(0,r.jsx)("span",{className:"text-xs ".concat(k?"font-tajawal":"font-sans"),style:{color:P.colors.text.secondary},children:_.label})]})}),U&&(0,r.jsx)(o.E.div,{className:"mt-2 text-sm text-red-400 ".concat(k?"text-right font-tajawal":"text-left font-sans"),initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:f})]})});m.displayName="AuthInput",a.Z=m},7786:function(e,a,t){var r=t(2322),s=t(2784),o=t(9442),n=t(8025),i=t(8508),l=t(5632),c=t(1675),d=t(5116);a.Z=e=>{let{isOpen:a,onClose:t,title:u,subtitle:m,description:g,children:h,size:p="md",showCloseButton:x=!0,closeOnOverlayClick:b=!0,className:E=""}=e,{currentTheme:f,themeName:N}=(0,c.Fg)(),v=(0,l.useRouter)(),{locale:w}=v,S="ar"===w,y="gold"===N;return(0,s.useEffect)(()=>(a?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[a]),(0,r.jsx)(o.u,{appear:!0,show:a,as:s.Fragment,children:(0,r.jsxs)(n.V,{as:"div",className:"relative z-50",onClose:b?t:()=>{},dir:S?"rtl":"ltr",children:[(0,r.jsx)(o.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0",style:{background:"rgba(0, 0, 0, 0.8)",backdropFilter:"blur(8px)",WebkitBackdropFilter:"blur(8px)"}})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(o.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsx)(n.V.Panel,{className:"\n                  w-full ".concat({sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl"}[p]," transform overflow-hidden rounded-2xl\n                  text-left align-middle shadow-xl transition-all relative\n                  ").concat(E,"\n                "),style:{background:y?"linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 20, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)":"linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 30, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)",backdropFilter:"blur(24px)",WebkitBackdropFilter:"blur(24px)",border:"1px solid rgba(255, 255, 255, 0.15)",boxShadow:y?"0 25px 50px -12px rgba(251, 191, 36, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)":"0 25px 50px -12px rgba(147, 51, 234, 0.25), 0 0 0 1px rgba(147, 51, 234, 0.1)"},children:(0,r.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.3},className:"relative",children:[x&&(0,r.jsx)(i.E.button,{type:"button",onClick:t,className:"\n                        absolute top-6 z-10 p-2 rounded-full transition-all duration-200\n                        ".concat(S?"left-6":"right-6","\n                        text-white/60 hover:text-white hover:bg-white/10\n                        focus:outline-none focus:ring-2 focus:ring-white/20\n                      "),whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(d.Z,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"px-8 pt-8 pb-6 ".concat(S?"text-right":"text-left"),children:[(0,r.jsx)(n.V.Title,{as:"h3",className:"\n                        text-3xl font-bold mb-3\n                        ".concat(S?"font-cairo text-arabic-premium":"font-sans","\n                      "),style:{color:f.colors.text.primary},children:u}),m&&(0,r.jsx)(i.E.p,{className:"\n                          text-lg font-medium mb-2\n                          ".concat(S?"font-tajawal":"font-sans","\n                        "),style:{color:f.colors.text.accent},initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},children:m}),g&&(0,r.jsx)(i.E.p,{className:"\n                          text-base leading-relaxed\n                          ".concat(S?"font-tajawal":"font-sans","\n                        "),style:{color:f.colors.text.secondary},initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:g})]}),(0,r.jsx)("div",{className:"px-8 pb-8",children:(0,r.jsx)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.4},children:h})}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 opacity-60",children:(0,r.jsx)("div",{className:"h-full rounded-t-2xl",style:{background:y?"linear-gradient(90deg, transparent 0%, rgba(251, 191, 36, 0.8) 50%, transparent 100%)":"linear-gradient(90deg, transparent 0%, rgba(147, 51, 234, 0.8) 50%, transparent 100%)"}})}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl overflow-hidden",children:(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:y?"linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.1) 50%, transparent 70%)":"linear-gradient(45deg, transparent 30%, rgba(147, 51, 234, 0.1) 50%, transparent 70%)",animation:"shimmer 3s ease-in-out infinite"}})})]})})})})})]})})}},4716:function(e,a,t){var r=t(2322),s=t(2784),o=t(8508),n=t(5632),i=t(34),l=t(1675),c=t(3848);a.Z=e=>{let{mode:a,onLoading:t,onError:d,disabled:u=!1,className:m=""}=e,[g,h]=s.useState(!1),{currentTheme:p,themeName:x}=(0,l.Fg)(),b=(0,n.useRouter)(),{locale:E}=b,{t:f}=(0,i.$G)("auth"),N="gold"===x,v=async()=>{if(!u&&!g)try{h(!0),null==t||t(!0);let e=await (0,c.signIn)("google",{redirect:!1,callbackUrl:"signin"===a?"/dashboard":"/onboarding"});if(null==e?void 0:e.error)throw Error(e.error)}catch(a){let e=a instanceof Error?a.message:f("login.networkError");null==d||d(e)}finally{h(!1),null==t||t(!1)}},w="\n    w-full h-14 px-6 rounded-xl font-semibold text-base\n    flex items-center justify-center gap-3 group\n    transition-all duration-300 ease-out\n    border border-white/20 backdrop-blur-xl\n    ".concat("ar"===E?"font-tajawal flex-row-reverse":"font-sans","\n    ").concat(u||g?"opacity-50 cursor-not-allowed":"hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98] cursor-pointer","\n    ").concat(m,"\n  "),S={background:N?"linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(126, 34, 206, 0.05) 100%)",color:p.colors.text.primary,boxShadow:N?"0 8px 32px rgba(251, 191, 36, 0.15)":"0 8px 32px rgba(147, 51, 234, 0.15)"};return(0,r.jsx)(o.E.button,{type:"button",onClick:v,disabled:u||g,className:w,style:S,whileHover:u||g?{}:{background:N?"linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(126, 34, 206, 0.08) 100%)",boxShadow:N?"0 12px 40px rgba(251, 191, 36, 0.25)":"0 12px 40px rgba(147, 51, 234, 0.25)"},whileTap:u||g?{}:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:f("modal.loading")})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-5 h-5 flex-shrink-0",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,r.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,r.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,r.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),(0,r.jsx)("span",{children:f("socialLogin.google")})]})})}},5392:function(e,a,t){var r=t(2322),s=t(2784),o=t(8508),n=t(5632),i=t(34),l=t(1675),c=t(8602),d=t(6734),u=t(7786),m=t(6809),g=t(4716),h=t(7330),p=t(666),x=t(1183),b=t(949);a.Z=e=>{let{isOpen:a,onClose:t,onSuccess:E,onError:f,onSwitchToSignUp:N}=e,[v,w]=(0,s.useState)({email:"",password:"",rememberMe:!1}),[S,y]=(0,s.useState)({}),[I,j]=(0,s.useState)({}),[T,A]=(0,s.useState)(!1),{t:_}=(0,i.$G)(["auth","common"]),{currentTheme:R,themeName:C}=(0,l.Fg)(),D=(0,n.useRouter)(),{locale:O}=D,P="ar"===O,F="gold"===C,L=(e,a)=>{w(t=>({...t,[e]:a})),S[e]&&y(a=>({...a,[e]:""}))},M=e=>{j(a=>({...a,[e]:!0})),k(e)},k=e=>{try{"email"===e?c.userLoginSchema.shape.email.parse(v.email):"password"===e&&c.userLoginSchema.shape.password.parse(v.password),y(a=>({...a,[e]:""}))}catch(a){a instanceof d.z.ZodError&&y(t=>({...t,[e]:_("auth:validation.".concat(a.errors[0].code))||a.errors[0].message}))}},z=()=>{try{return c.userLoginSchema.parse(v),y({}),!0}catch(e){if(e instanceof d.z.ZodError){let a={};e.errors.forEach(e=>{e.path[0]&&(a[e.path[0]]=_("auth:validation.".concat(e.code))||e.message)}),y(a),j({email:!0,password:!0})}return!1}},U=async e=>{if(e.preventDefault(),z()){A(!0);try{await new Promise(e=>setTimeout(e,2e3)),E&&E({user:{email:v.email},token:"mock-token"}),t()}catch(a){let e=_("auth:login.networkError");y({general:e}),f&&f(e)}finally{A(!1)}}},V="\n    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300\n    flex items-center justify-center gap-3 group relative overflow-hidden\n    ".concat(P?"font-tajawal":"font-sans","\n    ").concat(T?"cursor-not-allowed opacity-70":"cursor-pointer","\n  ");return(0,r.jsx)(u.Z,{isOpen:a,onClose:t,title:_("auth:login.title"),subtitle:_("auth:login.subtitle"),description:_("auth:login.description"),size:"md",children:(0,r.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[S.general&&(0,r.jsx)(o.E.div,{className:"p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},children:S.general}),(0,r.jsx)(m.Z,{label:_("auth:login.email"),placeholder:_("auth:login.emailPlaceholder"),type:"email",value:v.email,onChange:e=>L("email",e),onBlur:()=>M("email"),error:S.email,touched:I.email,required:!0,autoComplete:"email",leftIcon:(0,r.jsx)(h.Z,{className:"w-5 h-5"})}),(0,r.jsx)(m.Z,{label:_("auth:login.password"),placeholder:_("auth:login.passwordPlaceholder"),type:"password",value:v.password,onChange:e=>L("password",e),onBlur:()=>M("password"),error:S.password,touched:I.password,required:!0,autoComplete:"current-password",leftIcon:(0,r.jsx)(p.Z,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex items-center justify-between ".concat(P?"flex-row-reverse":""),children:[(0,r.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer group",children:[(0,r.jsx)("input",{type:"checkbox",checked:v.rememberMe,onChange:e=>L("rememberMe",e.target.checked),className:"w-4 h-4 rounded border-white/20 bg-white/10 text-current focus:ring-2 focus:ring-current"}),(0,r.jsx)("span",{className:"text-sm ".concat(P?"font-tajawal":"font-sans"),style:{color:R.colors.text.secondary},children:_("auth:login.rememberMe")})]}),(0,r.jsx)("button",{type:"button",className:"text-sm transition-colors duration-200 ".concat(P?"font-tajawal":"font-sans"),style:{color:R.colors.text.accent},children:_("auth:login.forgotPassword")})]}),(0,r.jsx)(o.E.button,{type:"submit",disabled:T,className:V,style:{background:F?"linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)",boxShadow:F?"0 8px 32px rgba(251, 191, 36, 0.3)":"0 8px 32px rgba(147, 51, 234, 0.3)"},whileHover:T?{}:{scale:1.02,y:-2},whileTap:T?{}:{scale:.98},children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),_("auth:login.loggingIn")]}):(0,r.jsxs)(r.Fragment,{children:[_("auth:login.loginButton"),P?(0,r.jsx)(x.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"}):(0,r.jsx)(b.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-white/20"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-4 ".concat(P?"font-tajawal":"font-sans"),style:{backgroundColor:"rgba(0, 0, 0, 0.8)",color:R.colors.text.secondary},children:_("auth:socialLogin.continueWith")})})]}),(0,r.jsx)(g.Z,{mode:"signin",onLoading:e=>A(e),onError:e=>y(a=>({...a,general:e})),disabled:T}),(0,r.jsxs)("div",{className:"text-center ".concat(P?"font-tajawal":"font-sans"),children:[(0,r.jsx)("span",{style:{color:R.colors.text.secondary},children:_("auth:login.noAccount")})," ",(0,r.jsx)("button",{type:"button",onClick:N,className:"font-semibold transition-colors duration-200 hover:underline",style:{color:R.colors.text.accent},children:_("auth:login.signUp")})]})]})})}},785:function(e,a,t){var r=t(2322),s=t(2784),o=t(8508),n=t(5632),i=t(34),l=t(1675),c=t(8602),d=t(6734),u=t(7786),m=t(6809),g=t(4716),h=t(3526),p=t(5374),x=t(7330),b=t(666),E=t(7767),f=t(924),N=t(7013),v=t(949),w=t(1183);a.Z=e=>{let{isOpen:a,onClose:t,onSuccess:S,onError:y,onSwitchToSignIn:I}=e,[j,T]=(0,s.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",role:"CLIENT",governorate:"",city:"",language:"ar",acceptTerms:!1}),[A,_]=(0,s.useState)({}),[R,C]=(0,s.useState)({}),[D,O]=(0,s.useState)(!1),[P,F]=(0,s.useState)(1),{t:L}=(0,i.$G)(["auth","common"]),{currentTheme:M,themeName:k}=(0,l.Fg)(),z=(0,n.useRouter)(),{locale:U}=z,V="ar"===U,Z="gold"===k,B=(0,s.useMemo)(()=>{let e=j.password,a=0,t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/[0-9]/.test(e),o=/[^A-Za-z0-9]/.test(e),n=e.length>=8;t&&a++,r&&a++,s&&a++,o&&a++,n&&a++;let i="weak";return a>=4?i="strong":a>=3?i="good":a>=2&&(i="fair"),{score:Math.min(a,4),label:i,hasUppercase:t,hasLowercase:r,hasNumber:s,hasSpecialChar:o,hasMinLength:n}},[j.password]),G=(0,s.useMemo)(()=>{let e=h.S.find(e=>e.governorate===j.governorate);return e?e.cities:[]},[j.governorate]),Y=(e,a)=>{T(t=>{let r={...t,[e]:a};return"governorate"===e&&(r.city=""),r}),A[e]&&_(a=>({...a,[e]:""}))},W=e=>{C(a=>({...a,[e]:!0})),X(e)},X=e=>{try{if("confirmPassword"===e){if(j.password!==j.confirmPassword)throw Error(L("auth:validation.confirmPassword"))}else{let a=c.userRegistrationSchema.shape[e];a&&a.parse(j[e])}_(a=>({...a,[e]:""}))}catch(a){a instanceof d.z.ZodError?_(t=>({...t,[e]:L("auth:validation.".concat(a.errors[0].code))||a.errors[0].message})):a instanceof Error&&_(t=>({...t,[e]:a.message}))}},H=e=>{let a=[];1===e?a.push("firstName","lastName","email","password","confirmPassword"):2===e&&(a.push("role"),j.phone&&a.push("phone"));let t=!0;return a.forEach(e=>{try{if("confirmPassword"===e){if(j.password!==j.confirmPassword)throw Error(L("auth:validation.confirmPassword"))}else{let a=c.userRegistrationSchema.shape[e];a&&a.parse(j[e])}_(a=>({...a,[e]:""}))}catch(a){t=!1,a instanceof d.z.ZodError?_(t=>({...t,[e]:L("auth:validation.".concat(a.errors[0].code))||a.errors[0].message})):a instanceof Error&&_(t=>({...t,[e]:a.message})),C(a=>({...a,[e]:!0}))}}),t},q=()=>{try{if(c.userRegistrationSchema.parse(j),j.password!==j.confirmPassword)return _(e=>({...e,confirmPassword:L("auth:validation.confirmPassword")})),!1;return _({}),!0}catch(e){if(e instanceof d.z.ZodError){let a={};e.errors.forEach(e=>{e.path[0]&&(a[e.path[0]]=L("auth:validation.".concat(e.code))||e.message)}),_(a);let t={};Object.keys(j).forEach(e=>{t[e]=!0}),C(t)}return!1}},K=async e=>{if(e.preventDefault(),q()){O(!0);try{await new Promise(e=>setTimeout(e,2e3)),S&&S({user:{email:j.email,role:j.role},token:"mock-token"}),t()}catch(a){let e=L("auth:register.networkError");_({general:e}),y&&y(e)}finally{O(!1)}}},$="\n    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300\n    flex items-center justify-center gap-3 group relative overflow-hidden\n    ".concat(V?"font-tajawal":"font-sans","\n    ").concat(D?"cursor-not-allowed opacity-70":"cursor-pointer","\n  "),Q={background:Z?"linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)":"linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)",boxShadow:Z?"0 8px 32px rgba(251, 191, 36, 0.3)":"0 8px 32px rgba(147, 51, 234, 0.3)"};return(0,r.jsx)(u.Z,{isOpen:a,onClose:t,title:L("auth:register.title"),subtitle:L("auth:register.subtitle"),description:L("auth:register.description"),size:"lg",children:(0,r.jsxs)("form",{onSubmit:K,className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,r.jsx)("div",{className:"flex items-center gap-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"\n                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold\n                    transition-all duration-300\n                    ".concat(e<=P?"text-white":"text-white/40","\n                  "),style:{background:e<=P?Z?"linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9))":"linear-gradient(135deg, rgba(147, 51, 234, 0.9), rgba(126, 34, 206, 0.9))":"rgba(255, 255, 255, 0.1)",border:"1px solid rgba(255, 255, 255, 0.2)"},children:e}),e<3&&(0,r.jsx)("div",{className:"w-8 h-0.5 mx-2 transition-all duration-300 ".concat(e<P?"opacity-100":"opacity-30"),style:{background:e<P?Z?"rgba(251, 191, 36, 0.6)":"rgba(147, 51, 234, 0.6)":"rgba(255, 255, 255, 0.2)"}})]},e))})}),A.general&&(0,r.jsx)(o.E.div,{className:"p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},children:A.general}),(0,r.jsxs)(o.E.div,{initial:{opacity:0,x:V?-20:20},animate:{opacity:1,x:0},exit:{opacity:0,x:V?20:-20},transition:{duration:.3},children:[1===P&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(m.Z,{label:L("auth:register.firstName"),placeholder:L("auth:register.firstNamePlaceholder"),value:j.firstName,onChange:e=>Y("firstName",e),onBlur:()=>W("firstName"),error:A.firstName,touched:R.firstName,required:!0,leftIcon:(0,r.jsx)(p.Z,{className:"w-5 h-5"})}),(0,r.jsx)(m.Z,{label:L("auth:register.lastName"),placeholder:L("auth:register.lastNamePlaceholder"),value:j.lastName,onChange:e=>Y("lastName",e),onBlur:()=>W("lastName"),error:A.lastName,touched:R.lastName,required:!0,leftIcon:(0,r.jsx)(p.Z,{className:"w-5 h-5"})})]}),(0,r.jsx)(m.Z,{label:L("auth:register.email"),placeholder:L("auth:register.emailPlaceholder"),type:"email",value:j.email,onChange:e=>Y("email",e),onBlur:()=>W("email"),error:A.email,touched:R.email,required:!0,autoComplete:"email",leftIcon:(0,r.jsx)(x.Z,{className:"w-5 h-5"})}),(0,r.jsx)(m.Z,{label:L("auth:register.password"),placeholder:L("auth:register.passwordPlaceholder"),type:"password",value:j.password,onChange:e=>Y("password",e),onBlur:()=>W("password"),error:A.password,touched:R.password,required:!0,autoComplete:"new-password",leftIcon:(0,r.jsx)(b.Z,{className:"w-5 h-5"}),showPasswordStrength:!0,passwordStrength:{score:B.score,label:L("auth:passwordStrength.".concat(B.label))}}),(0,r.jsx)(m.Z,{label:L("auth:register.confirmPassword"),placeholder:L("auth:register.confirmPasswordPlaceholder"),type:"password",value:j.confirmPassword,onChange:e=>Y("confirmPassword",e),onBlur:()=>W("confirmPassword"),error:A.confirmPassword,touched:R.confirmPassword,required:!0,autoComplete:"new-password",leftIcon:(0,r.jsx)(b.Z,{className:"w-5 h-5"})})]}),2===P&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(m.Z,{label:L("auth:register.phone"),placeholder:L("auth:register.phonePlaceholder"),type:"tel",value:j.phone||"",onChange:e=>Y("phone",e),onBlur:()=>W("phone"),error:A.phone,touched:R.phone,leftIcon:(0,r.jsx)(E.Z,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"block text-lg font-semibold ".concat(V?"text-right font-tajawal":"text-left font-sans"),style:{color:M.colors.text.primary},children:[L("auth:register.role")," ",(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(o.E.label,{className:"\n                      relative p-6 rounded-xl cursor-pointer transition-all duration-300\n                      border-2 ".concat("CLIENT"===j.role?Z?"border-amber-400/50":"border-purple-400/50":"border-white/20","\n                    "),style:{background:"CLIENT"===j.role?Z?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsx)("input",{type:"radio",name:"role",value:"CLIENT",checked:"CLIENT"===j.role,onChange:e=>Y("role",e.target.value),className:"sr-only"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 ".concat(V?"flex-row-reverse text-right":"text-left"),children:[(0,r.jsx)(f.Z,{className:"w-8 h-8 text-blue-400"}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"font-semibold text-lg ".concat(V?"font-tajawal":"font-sans"),style:{color:M.colors.text.primary},children:L("auth:register.client")})})]})]}),(0,r.jsxs)(o.E.label,{className:"\n                      relative p-6 rounded-xl cursor-pointer transition-all duration-300\n                      border-2 ".concat("EXPERT"===j.role?Z?"border-amber-400/50":"border-purple-400/50":"border-white/20","\n                    "),style:{background:"EXPERT"===j.role?Z?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsx)("input",{type:"radio",name:"role",value:"EXPERT",checked:"EXPERT"===j.role,onChange:e=>Y("role",e.target.value),className:"sr-only"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 ".concat(V?"flex-row-reverse text-right":"text-left"),children:[(0,r.jsx)(N.Z,{className:"w-8 h-8 text-green-400"}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"font-semibold text-lg ".concat(V?"font-tajawal":"font-sans"),style:{color:M.colors.text.primary},children:L("auth:register.expert")})})]})]})]}),A.role&&R.role&&(0,r.jsx)("div",{className:"text-sm text-red-400 ".concat(V?"text-right font-tajawal":"text-left font-sans"),children:A.role})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium ".concat(V?"text-right font-tajawal":"text-left font-sans"),style:{color:M.colors.text.secondary},children:L("auth:register.governorate")}),(0,r.jsxs)("select",{value:j.governorate,onChange:e=>Y("governorate",e.target.value),onBlur:()=>W("governorate"),className:"\n                      w-full h-14 px-4 rounded-xl transition-all duration-300\n                      ".concat(V?"text-right font-tajawal":"text-left font-sans","\n                      text-white bg-white/8 border border-white/20\n                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      ").concat(Z?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50","\n                    "),style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},children:[(0,r.jsx)("option",{value:"",children:L("auth:register.governoratePlaceholder")}),h.S.map(e=>(0,r.jsx)("option",{value:e.governorate,className:"bg-gray-800",children:e.governorate},e.governorate))]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium ".concat(V?"text-right font-tajawal":"text-left font-sans"),style:{color:M.colors.text.secondary},children:L("auth:register.city")}),(0,r.jsxs)("select",{value:j.city,onChange:e=>Y("city",e.target.value),onBlur:()=>W("city"),disabled:!j.governorate,className:"\n                      w-full h-14 px-4 rounded-xl transition-all duration-300\n                      ".concat(V?"text-right font-tajawal":"text-left font-sans","\n                      text-white bg-white/8 border border-white/20\n                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      disabled:opacity-50 disabled:cursor-not-allowed\n                      ").concat(Z?"focus:ring-amber-400/50 focus:border-amber-400/50":"focus:ring-purple-400/50 focus:border-purple-400/50","\n                    "),style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},children:[(0,r.jsx)("option",{value:"",children:L("auth:register.cityPlaceholder")}),G.map(e=>(0,r.jsx)("option",{value:e,className:"bg-gray-800",children:e},e))]})]})]})]}),3===P&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"block text-lg font-semibold ".concat(V?"text-right font-tajawal":"text-left font-sans"),style:{color:M.colors.text.primary},children:L("auth:register.language")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)(o.E.label,{className:"\n                      relative p-4 rounded-xl cursor-pointer transition-all duration-300\n                      border-2 ".concat("ar"===j.language?Z?"border-amber-400/50":"border-purple-400/50":"border-white/20","\n                    "),style:{background:"ar"===j.language?Z?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsx)("input",{type:"radio",name:"language",value:"ar",checked:"ar"===j.language,onChange:e=>Y("language",e.target.value),className:"sr-only"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("div",{className:"font-semibold ".concat(V?"font-tajawal":"font-sans"),style:{color:M.colors.text.primary},children:L("auth:register.arabic")})})]}),(0,r.jsxs)(o.E.label,{className:"\n                      relative p-4 rounded-xl cursor-pointer transition-all duration-300\n                      border-2 ".concat("en"===j.language?Z?"border-amber-400/50":"border-purple-400/50":"border-white/20","\n                    "),style:{background:"en"===j.language?Z?"rgba(251, 191, 36, 0.1)":"rgba(147, 51, 234, 0.1)":"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsx)("input",{type:"radio",name:"language",value:"en",checked:"en"===j.language,onChange:e=>Y("language",e.target.value),className:"sr-only"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("div",{className:"font-semibold ".concat(V?"font-tajawal":"font-sans"),style:{color:M.colors.text.primary},children:L("auth:register.english")})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer group",children:[(0,r.jsx)("input",{type:"checkbox",checked:j.acceptTerms,onChange:e=>Y("acceptTerms",e.target.checked),className:"\n                      mt-1 w-5 h-5 rounded border-white/20 bg-white/10\n                      focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      ".concat(Z?"text-amber-400 focus:ring-amber-400/50":"text-purple-400 focus:ring-purple-400/50","\n                    ")}),(0,r.jsxs)("span",{className:"text-sm leading-relaxed ".concat(V?"font-tajawal text-right":"font-sans text-left"),style:{color:M.colors.text.secondary},children:[L("auth:register.acceptTerms")," ",(0,r.jsx)("button",{type:"button",className:"font-semibold transition-colors duration-200 hover:underline",style:{color:M.colors.text.accent},children:L("auth:register.termsOfService")})," ",L("auth:register.and")," ",(0,r.jsx)("button",{type:"button",className:"font-semibold transition-colors duration-200 hover:underline",style:{color:M.colors.text.accent},children:L("auth:register.privacyPolicy")})]})]}),A.acceptTerms&&R.acceptTerms&&(0,r.jsx)("div",{className:"text-sm text-red-400 ".concat(V?"text-right font-tajawal":"text-left font-sans"),children:A.acceptTerms})]})]})]},P),(0,r.jsxs)("div",{className:"flex gap-4 ".concat(1===P?"justify-end":"justify-between"),children:[P>1&&(0,r.jsxs)(o.E.button,{type:"button",onClick:()=>{F(e=>e-1)},className:$,style:{background:"rgba(255, 255, 255, 0.08)",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.2)"},whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},children:[V?(0,r.jsx)(v.Z,{className:"w-5 h-5"}):(0,r.jsx)(w.Z,{className:"w-5 h-5"}),L("common:actions.previous")]}),P<3?(0,r.jsxs)(o.E.button,{type:"button",onClick:()=>{H(P)&&F(e=>e+1)},className:$,style:Q,whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},children:[L("common:actions.next"),V?(0,r.jsx)(w.Z,{className:"w-5 h-5"}):(0,r.jsx)(v.Z,{className:"w-5 h-5"})]}):(0,r.jsx)(o.E.button,{type:"submit",disabled:D,className:$,style:Q,whileHover:D?{}:{scale:1.02,y:-2},whileTap:D?{}:{scale:.98},children:D?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),L("auth:register.registering")]}):(0,r.jsxs)(r.Fragment,{children:[L("auth:register.registerButton"),V?(0,r.jsx)(w.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"}):(0,r.jsx)(v.Z,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})})]}),1===P&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-white/20"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-4 ".concat(V?"font-tajawal":"font-sans"),style:{backgroundColor:M.colors.neutral[500],color:M.colors.text.secondary},children:L("auth:socialLogin.continueWith")})})]}),(0,r.jsx)(g.Z,{mode:"signup",onLoading:e=>O(e),onError:e=>_(a=>({...a,general:e})),disabled:D})]}),(0,r.jsxs)("div",{className:"text-center ".concat(V?"font-tajawal":"font-sans"),children:[(0,r.jsx)("span",{style:{color:M.colors.text.secondary},children:L("auth:register.haveAccount")})," ",(0,r.jsx)("button",{type:"button",onClick:I,className:"font-semibold transition-colors duration-200 hover:underline",style:{color:M.colors.text.accent},children:L("auth:register.signIn")})]})]})})}},3526:function(e,a,t){t.d(a,{S:function(){return r}});let r=[{governorate:"دمشق",cities:["دمشق","جرمانا","دوما","داريا","قدسيا","صحنايا"]},{governorate:"حلب",cities:["حلب","منبج","عفرين","جرابلس","الباب","أعزاز"]},{governorate:"حمص",cities:["حمص","تدمر","القريتين","الرستن","تلبيسة","صدد"]},{governorate:"حماة",cities:["حماة","سلمية","مصياف","محردة","السقيلبية","قلعة المضيق"]},{governorate:"اللاذقية",cities:["اللاذقية","جبلة","بانياس","القرداحة","الحفة","كسب"]},{governorate:"طرطوس",cities:["طرطوس","بانياس","صافيتا","دريكيش","الشيخ بدر","القدموس"]},{governorate:"إدلب",cities:["إدلب","جسر الشغور","أريحا","معرة النعمان","سراقب","خان شيخون"]},{governorate:"درعا",cities:["درعا","إزرع","الصنمين","نوى","جاسم","الشيخ مسكين"]},{governorate:"السويداء",cities:["السويداء","صلخد","شهبا","قنوات","المزرعة","عرى"]},{governorate:"القنيطرة",cities:["القنيطرة","فيق","خان أرنبة","مسعدة","جباتا الخشب","عين التينة"]},{governorate:"الرقة",cities:["الرقة","تل أبيض","الثورة","سلوك","كسرة","الجرنية"]},{governorate:"دير الزور",cities:["دير الزور","الميادين","البوكمال","الأشارة","التيم","الصالحية"]},{governorate:"الحسكة",cities:["الحسكة","القامشلي","رأس العين","المالكية","عامودا","الدرباسية"]},{governorate:"ريف دمشق",cities:["التل","النبك","يبرود","القطيفة","الزبداني","مضايا"]}]},308:function(e,a,t){var r=this&&this.__createBinding||(Object.create?function(e,a,t,r){void 0===r&&(r=t);var s=Object.getOwnPropertyDescriptor(a,t);(!s||("get"in s?!a.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return a[t]}}),Object.defineProperty(e,r,s)}:function(e,a,t,r){void 0===r&&(r=t),e[r]=a[t]}),s=this&&this.__exportStar||function(e,a){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(a,t)||r(a,e,t)};Object.defineProperty(a,"__esModule",{value:!0}),a.sanitizeInput=a.isStrongPassword=a.normalizeEmail=a.isValidEmail=a.generateNumericCode=a.generateRandomString=void 0,s(t(6164),a),a.generateRandomString=(e=32)=>{let a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let r=0;r<e;r++)t+=a.charAt(Math.floor(Math.random()*a.length));return t},a.generateNumericCode=(e=6)=>{let a="0123456789",t="";for(let r=0;r<e;r++)t+=a.charAt(Math.floor(Math.random()*a.length));return t},a.isValidEmail=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),a.normalizeEmail=e=>e.toLowerCase().trim(),a.isStrongPassword=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(e),a.sanitizeInput=e=>e.trim().replace(/[<>]/g,"")},8812:function(e,a){Object.defineProperty(a,"__esModule",{value:!0}),a.ERROR_CODES=a.NOTIFICATION_TYPES=a.PASSWORD_RESET=a.EMAIL_VERIFICATION=a.SESSION_CONFIG=a.CACHE_TTL=a.RATE_LIMITS=a.TEXT_LIMITS=a.REVISION_LIMITS=a.DELIVERY_TIME_RANGES=a.PRICE_RANGES=a.RATING_SCALE=a.PAGINATION_DEFAULTS=a.FILE_SIZE_LIMITS=a.ALLOWED_VIDEO_TYPES=a.ALLOWED_DOCUMENT_TYPES=a.ALLOWED_IMAGE_TYPES=a.LANGUAGES=a.CURRENCIES=a.PAYMENT_METHODS=a.PAYMENT_STATUSES=a.BOOKING_STATUSES=a.SERVICE_STATUSES=a.USER_STATUSES=a.USER_ROLES=a.SERVICE_CATEGORIES_EN=a.SERVICE_CATEGORIES=a.SYRIAN_GOVERNORATES_EN=a.SYRIAN_GOVERNORATES=void 0,a.SYRIAN_GOVERNORATES=["دمشق","ريف دمشق","حلب","حمص","حماة","اللاذقية","إدلب","الحسكة","دير الزور","الرقة","درعا","السويداء","القنيطرة","طرطوس"],a.SYRIAN_GOVERNORATES_EN=["Damascus","Damascus Countryside","Aleppo","Homs","Hama","Latakia","Idlib","Al-Hasakah","Deir ez-Zor","Raqqa","Daraa","As-Suwayda","Quneitra","Tartus"],a.SERVICE_CATEGORIES=["تطوير البرمجيات","التصميم الجرافيكي","التسويق الرقمي","الكتابة والترجمة","التصوير والفيديو","الاستشارات","التعليم والتدريب","الخدمات المالية","الهندسة والعمارة","الطب والصحة"],a.SERVICE_CATEGORIES_EN=["Software Development","Graphic Design","Digital Marketing","Writing & Translation","Photography & Video","Consulting","Education & Training","Financial Services","Engineering & Architecture","Medicine & Health"],a.USER_ROLES=["CLIENT","EXPERT","ADMIN"],a.USER_STATUSES=["ACTIVE","INACTIVE","SUSPENDED","PENDING_VERIFICATION"],a.SERVICE_STATUSES=["DRAFT","PENDING_REVIEW","ACTIVE","PAUSED","REJECTED","ARCHIVED"],a.BOOKING_STATUSES=["PENDING","ACCEPTED","IN_PROGRESS","DELIVERED","REVISION_REQUESTED","COMPLETED","CANCELLED","DISPUTED","REFUNDED"],a.PAYMENT_STATUSES=["PENDING","PROCESSING","COMPLETED","FAILED","CANCELLED","REFUNDED","DISPUTED","CHARGEBACK"],a.PAYMENT_METHODS=["CREDIT_CARD","DEBIT_CARD","PAYPAL","BANK_TRANSFER","MOBILE_WALLET","CRYPTOCURRENCY","CASH"],a.CURRENCIES=["USD","SYP"],a.LANGUAGES=["ar","en"],a.ALLOWED_IMAGE_TYPES=["image/jpeg","image/jpg","image/png","image/gif","image/webp"],a.ALLOWED_DOCUMENT_TYPES=["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"],a.ALLOWED_VIDEO_TYPES=["video/mp4","video/avi","video/mov","video/wmv","video/webm"],a.FILE_SIZE_LIMITS={AVATAR:5242880,PORTFOLIO:********,DOCUMENT:********,VIDEO:*********},a.PAGINATION_DEFAULTS={PAGE:1,LIMIT:10,MAX_LIMIT:100},a.RATING_SCALE={MIN:1,MAX:5},a.PRICE_RANGES={MIN_USD:5,MAX_USD:1e4,MIN_SYP:1e4,MAX_SYP:2e7},a.DELIVERY_TIME_RANGES={MIN:1,MAX:365},a.REVISION_LIMITS={MIN:0,MAX:10},a.TEXT_LIMITS={TITLE_MIN:10,TITLE_MAX:100,DESCRIPTION_MIN:50,DESCRIPTION_MAX:2e3,BIO_MAX:500,MESSAGE_MAX:2e3,REVIEW_TITLE_MAX:100,REVIEW_COMMENT_MAX:1e3},a.RATE_LIMITS={GENERAL:{WINDOW_MS:9e5,MAX_REQUESTS:100},AUTH:{WINDOW_MS:9e5,MAX_REQUESTS:5},UPLOAD:{WINDOW_MS:6e4,MAX_REQUESTS:10}},a.CACHE_TTL={USER_DATA:3600,SERVICE_DATA:1800,CATEGORY_DATA:86400,SEARCH_RESULTS:300},a.SESSION_CONFIG={ACCESS_TOKEN_EXPIRES:900,REFRESH_TOKEN_EXPIRES:604800,REMEMBER_ME_EXPIRES:2592e3},a.EMAIL_VERIFICATION={TOKEN_EXPIRES:86400,RESEND_COOLDOWN:300},a.PASSWORD_RESET={TOKEN_EXPIRES:3600,REQUEST_COOLDOWN:300},a.NOTIFICATION_TYPES=["BOOKING_CREATED","BOOKING_ACCEPTED","BOOKING_REJECTED","BOOKING_COMPLETED","BOOKING_CANCELLED","PAYMENT_RECEIVED","PAYMENT_FAILED","MESSAGE_RECEIVED","REVIEW_RECEIVED","SERVICE_APPROVED","SERVICE_REJECTED","ACCOUNT_VERIFIED","ACCOUNT_SUSPENDED"],a.ERROR_CODES={INVALID_CREDENTIALS:"INVALID_CREDENTIALS",TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",SESSION_EXPIRED:"SESSION_EXPIRED",INSUFFICIENT_PERMISSIONS:"INSUFFICIENT_PERMISSIONS",ACCESS_DENIED:"ACCESS_DENIED",VALIDATION_ERROR:"VALIDATION_ERROR",REQUIRED_FIELD:"REQUIRED_FIELD",INVALID_FORMAT:"INVALID_FORMAT",NOT_FOUND:"NOT_FOUND",ALREADY_EXISTS:"ALREADY_EXISTS",DUPLICATE_ENTRY:"DUPLICATE_ENTRY",RATE_LIMIT_EXCEEDED:"RATE_LIMIT_EXCEEDED",FILE_TOO_LARGE:"FILE_TOO_LARGE",INVALID_FILE_TYPE:"INVALID_FILE_TYPE",PAYMENT_FAILED:"PAYMENT_FAILED",INSUFFICIENT_FUNDS:"INSUFFICIENT_FUNDS",INTERNAL_ERROR:"INTERNAL_ERROR",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE"}},8602:function(e,a,t){var r=this&&this.__createBinding||(Object.create?function(e,a,t,r){void 0===r&&(r=t);var s=Object.getOwnPropertyDescriptor(a,t);(!s||("get"in s?!a.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return a[t]}}),Object.defineProperty(e,r,s)}:function(e,a,t,r){void 0===r&&(r=t),e[r]=a[t]}),s=this&&this.__exportStar||function(e,a){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(a,t)||r(a,e,t)};Object.defineProperty(a,"__esModule",{value:!0}),s(t(6164),a),s(t(8812),a),s(t(308),a)},6164:function(e,a,t){Object.defineProperty(a,"__esModule",{value:!0}),a.validateAudioFile=a.validateVideoFile=a.validateDocumentFile=a.validateImageFile=a.validateFileType=a.validateExpiryDate=a.validateCVV=a.validateCreditCard=a.validateSyrianPhone=a.validateEnglishText=a.validateArabicText=a.validateSlug=a.validateUrl=a.validatePhone=a.validatePassword=a.validateEmail=a.fileUploadSchema=a.reviewSchema=a.messageSchema=a.bookingSchema=a.serviceSchema=a.userProfileSchema=a.userLoginSchema=a.userRegistrationSchema=a.slugSchema=a.urlSchema=a.phoneSchema=a.passwordSchema=a.emailSchema=void 0;let r=t(6458);a.emailSchema=r.z.string().email("Invalid email address"),a.passwordSchema=r.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),a.phoneSchema=r.z.string().regex(/^(\+963|0)?[0-9]{8,9}$/,"Invalid Syrian phone number"),a.urlSchema=r.z.string().url("Invalid URL"),a.slugSchema=r.z.string().regex(/^[a-z0-9-]+$/,"Slug can only contain lowercase letters, numbers, and hyphens").min(3,"Slug must be at least 3 characters").max(50,"Slug must be at most 50 characters"),a.userRegistrationSchema=r.z.object({email:a.emailSchema,password:a.passwordSchema,firstName:r.z.string().min(2,"First name must be at least 2 characters"),lastName:r.z.string().min(2,"Last name must be at least 2 characters"),phone:a.phoneSchema.optional(),role:r.z.enum(["CLIENT","EXPERT","client","expert"]).transform(e=>e.toUpperCase()),language:r.z.enum(["ar","en"]).default("ar"),acceptTerms:r.z.boolean().refine(e=>!0===e,"You must accept the terms")}),a.userLoginSchema=r.z.object({email:a.emailSchema,password:r.z.string().min(1,"Password is required"),rememberMe:r.z.boolean().optional()}),a.userProfileSchema=r.z.object({firstName:r.z.string().min(2,"First name must be at least 2 characters"),lastName:r.z.string().min(2,"Last name must be at least 2 characters"),phone:a.phoneSchema.optional(),bio:r.z.string().max(500,"Bio must be at most 500 characters").optional(),website:a.urlSchema.optional(),location:r.z.object({governorate:r.z.string(),city:r.z.string(),district:r.z.string().optional()}).optional()}),a.serviceSchema=r.z.object({title:r.z.object({ar:r.z.string().min(10,"Arabic title must be at least 10 characters"),en:r.z.string().min(10,"English title must be at least 10 characters").optional()}),description:r.z.object({ar:r.z.string().min(50,"Arabic description must be at least 50 characters"),en:r.z.string().min(50,"English description must be at least 50 characters").optional()}),categoryId:r.z.string().min(1,"Category is required"),tags:r.z.array(r.z.string()).min(1,"At least one tag is required").max(10,"Maximum 10 tags allowed"),pricing:r.z.object({type:r.z.enum(["fixed","hourly","package","custom"]),basePrice:r.z.number().min(5,"Minimum price is $5"),currency:r.z.enum(["USD","SYP"]).default("USD")}),deliveryTime:r.z.number().min(1,"Delivery time must be at least 1 day").max(365,"Maximum delivery time is 365 days"),revisions:r.z.number().min(0,"Revisions cannot be negative").max(10,"Maximum 10 revisions")}),a.bookingSchema=r.z.object({serviceId:r.z.string().min(1,"Service is required"),packageId:r.z.string().optional(),requirements:r.z.array(r.z.object({questionId:r.z.string(),answer:r.z.union([r.z.string(),r.z.array(r.z.string())])})),customInstructions:r.z.string().max(1e3,"Instructions must be at most 1000 characters").optional(),preferredDeliveryDate:r.z.date().optional()}),a.messageSchema=r.z.object({content:r.z.string().min(1,"Message cannot be empty").max(2e3,"Message must be at most 2000 characters"),type:r.z.enum(["text","file","image"]).default("text")}),a.reviewSchema=r.z.object({rating:r.z.number().min(1,"Rating must be at least 1").max(5,"Rating must be at most 5"),title:r.z.string().max(100,"Title must be at most 100 characters").optional(),comment:r.z.string().max(1e3,"Comment must be at most 1000 characters").optional(),wouldRecommend:r.z.boolean()}),a.fileUploadSchema=r.z.object({filename:r.z.string().min(1,"Filename is required"),mimeType:r.z.string().min(1,"MIME type is required"),size:r.z.number().max(********,"File size must be less than 10MB")}),a.validateEmail=e=>a.emailSchema.safeParse(e).success,a.validatePassword=e=>a.passwordSchema.safeParse(e).success,a.validatePhone=e=>a.phoneSchema.safeParse(e).success,a.validateUrl=e=>a.urlSchema.safeParse(e).success,a.validateSlug=e=>a.slugSchema.safeParse(e).success,a.validateArabicText=e=>/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(e),a.validateEnglishText=e=>/^[a-zA-Z\s.,!?'"()-]+$/.test(e),a.validateSyrianPhone=e=>/^(\+963|0)?[0-9]{8,9}$/.test(e),a.validateCreditCard=e=>{let a=e.replace(/\s/g,"");if(!/^[0-9]{13,19}$/.test(a))return!1;let t=0,r=!1;for(let e=a.length-1;e>=0;e--){let s=parseInt(a[e]);r&&(s*=2)>9&&(s-=9),t+=s,r=!r}return t%10==0},a.validateCVV=(e,a)=>"amex"===a?/^[0-9]{4}$/.test(e):/^[0-9]{3}$/.test(e),a.validateExpiryDate=(e,a)=>{let t=new Date,r=t.getFullYear(),s=t.getMonth()+1,o=parseInt(e),n=parseInt(a);return!(o<1)&&!(o>12)&&!(n<r)&&(n!==r||!(o<s))},a.validateFileType=(e,a)=>{let t=e.split(".").pop()?.toLowerCase();return!!t&&a.includes(t)},a.validateImageFile=e=>(0,a.validateFileType)(e,["jpg","jpeg","png","gif","webp","svg"]),a.validateDocumentFile=e=>(0,a.validateFileType)(e,["pdf","doc","docx","txt","rtf"]),a.validateVideoFile=e=>(0,a.validateFileType)(e,["mp4","avi","mov","wmv","flv","webm"]),a.validateAudioFile=e=>(0,a.validateFileType)(e,["mp3","wav","ogg","aac","flac"])}}]);